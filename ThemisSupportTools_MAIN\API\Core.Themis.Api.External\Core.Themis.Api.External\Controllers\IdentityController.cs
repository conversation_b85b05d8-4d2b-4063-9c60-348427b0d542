﻿using AutoMapper;
using Core.Themis.Libraries.BLL.AccessControl.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.OrderDetails.Interfaces;
using Core.Themis.Libraries.DTO.exposedObjects;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.AspNetCore.Authorization;
using NLog.Fluent;
using System.Text;
using System.Security.Cryptography;
using Core.Themis.Libraries.Utilities.Logging;
using WebApi.OutputCache.V2;
using Core.Themis.Libraries.Utilities.Crypto;
using Microsoft.Net.Http.Headers;
using Microsoft.Extensions.Logging;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Identity;
using Core.Themis.Libraries.BLL;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using System.Text.Json;
using System.Net;
using Core.Themis.Libraries.Utilities.CustomErrors;
using MimeKit.Cryptography;

namespace Core.Themis.Api.External.Controllers
{
    /// <summary>
    /// Work on identities (=customers)
    /// </summary>
    [ApiController]
    [ApiExplorerSettings(GroupName = "ext")]
    public class IdentityController : ControllerBase
    {

        private readonly IMapper _mapper;
        private readonly IIdentiteManager _identiteManager;
        private readonly IGlobalAppellationManager _globalAppellationManager;

        public IdentityController(
            IConfiguration configuration,
            IMemoryCache memoryCache,
            IIdentiteManager identiteManager,
            IGlobalAppellationManager globalAppellationManager,
            IMapper mapper
            )
        {
            _mapper = mapper;
            _globalAppellationManager = globalAppellationManager;
            _identiteManager = identiteManager;

        }

        private static readonly RodrigueNLogger Logger = new RodrigueNLogger();


        /// <summary>
        /// Get identity via email / b64password             
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="email">Email</param>
        /// <param name="b64password">Encrypted passw</param>
        /// <returns>Identity</returns>
        /// <remarks>
        /// 
        /// Example of request:
        /// 
        /// Returns identity account via password
        /// 
        ///     GET identity?email=pgautiedrigue.fr b64password=6e/pD3ksBVgmMDgPLOPr98ZQGyhORmUARKxjX9mlT8
        ///     {
        ///     }
        ///            
        /// </remarks>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Identity))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status406NotAcceptable)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpGet]
        [Authorize(Roles = "Admin,IdentiteManager")]
        [CacheOutput(ServerTimeSpan = 10, ClientTimeSpan = 10, ExcludeQueryStringFromCacheKey = false)]
        [Route("api/{structureId}/identity")]
        public IActionResult getIdentityByEmailOrId_B64password(int structureId, [FromQuery] int id = 0, [FromQuery] string? email = "", [FromQuery] string? b64password = "")
        {
            Logger.Debug(structureId, $"getIdentityByEmailB64password({email}, {id}, {b64password})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                if (!string.IsNullOrEmpty(b64password) && !Encryption64.IsBase64String(b64password))
                {
                    return Problem($"{b64password} is not a valid b64 string", null, StatusCodes.Status400BadRequest);
                }

                if (id == 0 && string.IsNullOrEmpty(email))
                {
                    return Problem($"need an email or an id", null, StatusCodes.Status400BadRequest);
                }
                IdentityDTO idDTO = new IdentityDTO();
                if (id == 0)
                {
                    idDTO = _identiteManager.Get(structureId, email, "fr", "");
                }
                else
                {
                    idDTO = _identiteManager.Get(structureId, id.ToString(), "fr", "");
                }

                if (idDTO != null && (string.IsNullOrEmpty(b64password) || idDTO.Password == b64password))
                {
                    Identity ide = (Identity)_mapper.Map(idDTO, typeof(IdentityDTO), typeof(Identity));

                    return Ok(ide);
                }
                else
                {
                    if (idDTO == null)
                    {
                        Logger.Error(structureId, $"getIdentityByEmailB64password({structureId}) :{email},{b64password} not found");
                    }
                    else
                    {
                        Logger.Error(structureId, $"getIdentityByEmailB64password({structureId}) :{email},{b64password} passw is not correct");
                    }
                    return Problem($"{email} not found", null, StatusCodes.Status404NotFound);
                }
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"getIdentityByEmailB64password({structureId}) :{email},{b64password} {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }

        }



        /// <summary>
        /// Update identity via email / b64password             
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="email">Email</param>
        /// <param name="b64password">Encrypted passw</param>
        /// <returns>Identity</returns>
        /// <remarks>
        /// 
        /// Update identity account via password
        /// 
        /// Example of request:        
        /// 
        ///     PATCH identity?email=pgautiedrigue.fr b64password=6e/pD3ksBVgmMDgPLOPr98ZQGyhORmUARKxjX9mlT8
        ///     {
        ///     }
        ///            
        /// </remarks>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Identity))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status406NotAcceptable)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpPatch]
        [Authorize(Roles = "Admin,IdentiteManager")]
        [CacheOutput(ServerTimeSpan = 10, ClientTimeSpan = 10, ExcludeQueryStringFromCacheKey = false)]
        [Route("api/{structureId}/identity")]
        public IActionResult updateIdentityByEmailOrId(int structureId, [FromBody] IdentityUpdate identity, [FromQuery] string? email = "", [FromQuery] int id = 0)
        {

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                Logger.Debug(structureId, $"updateIdentityByEmail({email}), {JsonSerializer.Serialize(identity).ToString()})...");

                if (identity.B64Password != "unchanged" && !Encryption64.IsBase64String(identity.B64Password))
                {
                    return Problem($"{identity.B64Password} is not a valid b64 string", null, StatusCodes.Status400BadRequest);
                }
                if (id == 0 && string.IsNullOrEmpty(email))
                {
                    return Problem($"need an email or an id", null, StatusCodes.Status400BadRequest);
                }

                IdentityDTOUpdate ideDTO = (IdentityDTOUpdate)_mapper.Map(identity, typeof(Identity), typeof(IdentityDTOUpdate));

                IdentityDTO idInit = new IdentityDTO();
                if (id == 0)
                {
                    idInit = _identiteManager.Get(structureId, email, "fr", "");
                }
                else
                {
                    idInit = _identiteManager.Get(structureId, id.ToString(), "fr", "");
                }

                if (idInit != null)
                {

                    try
                    {

                        ideDTO.IdentiteId = idInit.IdentiteId;

                        var r = _identiteManager.Update(structureId, ideDTO, true);

                        var idFinal = _identiteManager.Get(structureId, ideDTO.IdentiteId.ToString(), "fr", "");

                        if (idFinal != null)
                        {
                            Identity ide = (Identity)_mapper.Map(idFinal, typeof(IdentityDTO), typeof(Identity));

                            Logger.Debug(structureId, $"updateIdentityByEmail({email}) (id {idFinal.IdentiteId}) updated");

                            return Ok(ide);
                        }
                        else
                        {
                            Logger.Error(structureId, $"updateIdentityByEmail({structureId}) :{email} not found");

                            return Problem($"{email} not found", null, StatusCodes.Status404NotFound);
                        }
                    }
                    catch (IdentityConflitEmail ex)
                    {

                        return Problem(ex.Message, null, StatusCodes.Status409Conflict);

                    }
                }
                else
                {
                    Logger.Error(structureId, $"updateIdentityByEmail({structureId}) :{email}, {id} not found");

                    return Problem($"{email} not found", null, StatusCodes.Status404NotFound);
                }
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"updateIdentityByEmail({structureId}, {email}, {id}) {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }

        }


        /// <summary>
        /// Create Identity
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="identity">Identity Object</param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(Identity))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status406NotAcceptable)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpPost]
        [Authorize(Roles = "Admin,IdentiteManager")]
        [CacheOutput(ServerTimeSpan = 10, ClientTimeSpan = 10, ExcludeQueryStringFromCacheKey = false)]
        [Route("api/{structureId}/identity")]
        public IActionResult createIdentity(int structureId, [FromBody] Identity identity)
        {

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                Logger.Debug(structureId, $"createIdentity({JsonSerializer.Serialize(identity).ToString()})...");

                if (string.IsNullOrEmpty(identity.B64Password) || !Encryption64.IsBase64String(identity.B64Password))
                {
                    return Problem($"B64Password is not valid (b64p = '{identity.B64Password}')", null, StatusCodes.Status400BadRequest);
                }
                if (string.IsNullOrEmpty(identity.Email) || identity.Email == IdentityUpdate.unchangedString)
                {
                    return Problem($"email is empty !", null, StatusCodes.Status400BadRequest);
                }

                IdentityDTO ideDTO = (IdentityDTO)_mapper.Map(identity, typeof(Identity), typeof(IdentityDTO));

                try
                {

                    var newIdIdentyId = _identiteManager.AddIdentity(structureId, ideDTO, true);

                    var identFinal = _identiteManager.Get(structureId, newIdIdentyId.ToString(), "fr", "");

                    if (identFinal != null)
                    {
                        Identity ide = (Identity)_mapper.Map(identFinal, typeof(IdentityDTO), typeof(Identity));

                        Logger.Debug(structureId, $"createIdentity ok (id {identFinal.IdentiteId})");

                        //return Content(HttpStatusCode.Created, "Message");
                        //return CreatedAtAction(nameof(CreateAsync_IActionResult), new { id = product.Id }, product);
                        return Created($"api/{structureId}/identity?id={ide.Id}", ide);
                    }
                    else
                    {
                        Logger.Error(structureId, $"createIdentity({structureId}) :{identFinal.IdentiteId} not found");

                        return Problem($"{identFinal.IdentiteId} not found", null, StatusCodes.Status404NotFound);
                    }
                }
                catch (IdentityConflitEmail ex)
                {

                    return Problem(ex.Message, null, StatusCodes.Status409Conflict);

                }

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"createIdentity({structureId}) : {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// Get Civility             
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="langIso">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <returns>Identity</returns>
        /// <remarks>
        /// 
        /// Example of request:   
        /// 
        /// Returns salutions list (Mr, Mme, Mlle etc...)
        /// 
        ///     GET identity/salutations
        ///     {
        ///     }
        ///            
        /// </remarks>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Salutation>))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status406NotAcceptable)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpGet]
        
        [CacheOutput(ServerTimeSpan = 10, ClientTimeSpan = 10, ExcludeQueryStringFromCacheKey = false)]
        [Route("api/{structureId}/identity/salutations/")]
        [Route("api/{structureId}/identity/salutations/{langIso}")]
        public IActionResult getSalutations(int structureId, string? langIso ="")
        {
            Logger.Debug(structureId, $"getSalutations({structureId})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {                

                var listGA = _globalAppellationManager.GetAll(structureId, langIso);
                List<Salutation> ide = (List<Salutation>)_mapper.Map(listGA, typeof(List<GlobalAppellationDTO>), typeof(List<Salutation>));


                return Ok(ide);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"getSalutions({structureId}) : {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }

        }



        /// <summary>
        /// Encode password(string)    
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="password">String to encrypt</param>
        /// <returns>Encrypted string</returns>
        /// <remarks>
        /// 
        ///  You should generate b64 password on your side. To check if you have to good algo.
        ///  This method encrypt a string sha256 b64 (to generate a password).  
        /// </remarks>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(string))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status406NotAcceptable)]
        [HttpGet]
        [Route("api/identity/encrypt/{password}")]
        public IActionResult EncodePassWord_RodrigueWay(string password)
        {

            Logger.Debug(0, "encryptPassword(" + password + ")...");

            string encryptedPassWord = "";
            using (var sha = SHA256.Create())
            {
                var computedHash = sha.ComputeHash(Encoding.Unicode.GetBytes(password));
                encryptedPassWord = Convert.ToBase64String(computedHash);
            }
            Logger.Debug(0, $"encryptPassword({password}) ok : {encryptedPassWord}");
            return Ok(encryptedPassWord);
        }
    }
}
