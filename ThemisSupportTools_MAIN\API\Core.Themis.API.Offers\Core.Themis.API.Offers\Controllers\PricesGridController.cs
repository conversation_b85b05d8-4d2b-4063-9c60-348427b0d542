﻿using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.adhesion_offres.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.adhesion_offres;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;


namespace Core.Themis.API.Offers.Controllers
{
    [ApiController]
    public class PricesGridController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IAdhesionCatalogManager _adhesionCatalogManager;
        private readonly IBasketManager _basketManager;
        private readonly ISponsorManager _sponsorManager;
        private readonly IBuyerProfilManager _buyerProfilManager;
        private readonly IEventManager _eventManager;
        private readonly IReservesManager _reservesManager;
        private readonly IGestionPlaceManager _gestionPlaceManager;
        private readonly IPriceManager _priceManager;
        
        private static readonly RodrigueNLogger Logger = new();

        public PricesGridController(
            IConfiguration configuration, 
            IAdhesionCatalogManager adhesionCatalogManager,
            IBasketManager basketManager,
            ISponsorManager sponsorManager,
            IMemoryCache memoryCache,
            IBuyerProfilManager buyerProfilManager,
            IEventManager eventManager,
            IReservesManager reservesManager,
            IGestionPlaceManager gestionPlaceManager,
            IPriceManager priceManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _adhesionCatalogManager = adhesionCatalogManager;
            _basketManager = basketManager;
            _sponsorManager = sponsorManager;
            _buyerProfilManager = buyerProfilManager;
            _eventManager = eventManager;
            _reservesManager = reservesManager;
            _gestionPlaceManager = gestionPlaceManager;
            _priceManager = priceManager;
        }

        /// <summary>
        /// seances de la manif ayant des tarifs
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="eventId"></param>
        /// <param name="identityId"></param>
        /// <param name="webUserId">0</param>
        /// <param name="buyerProfilId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<EventDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpGet]
        //[Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/Sessions/{eventId}/{langCode}/{identityId}/{webUserId}/{buyerProfilId}")]
        [Route("api/{structureId}/Sessions/{eventId}/{langCode}/{identityId}/{webUserId}/{bpLogin}/{bpPassword}")]
        public IActionResult LoadSessions(int structureId, string langCode, int eventId, int identityId, int webUserId, int buyerProfilId, string bpLogin, string bpPassword)
        {
            Logger.Debug(structureId, $"LoadSessions({structureId},{langCode},{eventId},{identityId},{webUserId},{buyerProfilId},{bpLogin},{bpPassword})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                int myBprofilId = 0;

                BuyerProfilDTO buyerProfil = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, buyerProfilId, bpLogin, bpPassword);
                if (buyerProfil != null)
                    myBprofilId = buyerProfil.Id;

                List<EventDTO> listRet = new List<EventDTO>() { _eventManager.GetEventSessions(structureId, langCode, eventId, identityId, myBprofilId, DateTime.Now, DateTime.Now.AddYears(10))
                };

                Logger.Debug(structureId, $"LoadSessions({structureId},{langCode},{eventId},{langCode},{identityId},{webUserId},{buyerProfilId}) ok, {listRet.Count} events to return");

                return Ok(listRet);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"LoadSessions({structureId},{langCode},{eventId},{identityId},{webUserId},{buyerProfilId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<EventDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 10)]
        [HttpGet]
        [Route("api/{structureId}/PricesGrid/{eventId}/{sessionId}/{langCode}/{bpLogin}/{bpPassword}")]
        public IActionResult LoadPricesGrid(int structureId, string langCode, int eventId, int sessionId, string bpLogin, string bpPassword)
        {
            return LoadPricesGrid(structureId, langCode, eventId, sessionId, 0, 0, 0, bpLogin, bpPassword, 0);
        }

            /// <summary>
            /// grille de tarif de la séance
            /// </summary>
            /// <param name="structureId"></param>
            /// <param name="langCode"></param>
            /// <param name="eventId"></param>
            /// <param name="sessionId"></param>
            /// <param name="identityId"></param>
            /// <param name="webUserId"></param>
            /// <param name="buyerProfilId"></param>
            /// <param name="basketId">panier en cours eventuel</param>
            /// <returns></returns>
            [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<EventDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 10)]
        [HttpGet]
        //  [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/PricesGrid/{eventId}/{sessionId}/{langCode}/{identityId}/{webUserId}/{buyerProfilId}")]        
        [Route("api/{structureId}/PricesGrid/{eventId}/{sessionId}/{langCode}/{identityId}/{webUserId}/{buyerProfilId}/{basketId}")]
        public IActionResult LoadPricesGrid(int structureId, string langCode, int eventId, int sessionId, int identityId, int webUserId, int buyerProfilId, string bpLogin, string bpPassword, int basketId)
        {
            Logger.Debug(structureId, $"LoadPricesGrid({structureId},{eventId},{sessionId},{langCode},{identityId},{webUserId},{buyerProfilId})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                var priceGrid = _priceManager.LoadPricesGrid(structureId, langCode, eventId, sessionId, identityId, webUserId, buyerProfilId, bpLogin, bpPassword, basketId);

                return Ok(priceGrid);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"LoadPricesGrid({structureId},{eventId},{sessionId},{langCode},{identityId},{webUserId},{buyerProfilId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
     

        /// <summary>
        /// liste event / sessions / grille tarif filtrés par filiere et operateur (ne tient pas compte des regles de ventes internet)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="listFilieres"></param>
        /// <param name="listOperators"></param>
        /// <param name="listEvents"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<EventDTO>))]
        [HttpGet]
        // [Authorize(Roles = "User,Viewer")]        
        [Route("api/{structureId}/PricesGrid")]
        public IActionResult GetAllPricesOfEvents(int structureId, string langCode, [FromQuery] List<int> listFilieres, [FromQuery] List<int> listOperators, [FromQuery] List<int> listEvents)
        {
            Logger.Debug(structureId, $"GetAllPricesOfEvents({structureId},{string.Join(";", listEvents)})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                List<EventDTO> listRet = _eventManager.GetEventsSessionsPrices_full(langCode, structureId, listOperators, listFilieres, listEvents);

                Logger.Debug(structureId, $"GetAllPricesOfEvents({structureId},{string.Join(";", listEvents)})) ok, {listRet.Count} reserves to return");
                return Ok(listRet);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"GetAllPricesOfEvents({structureId},{string.Join(";", listEvents)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// obtenir les event / session / zone / floor / section / categ / price, filtrés par filiere et operateur (ne tient pas compte des regles de ventes internet)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="listFilieres"></param>
        /// <param name="listOperators"></param>
        /// <param name="listEvents"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<EventDTO>))]
        [HttpGet]
        // [Authorize(Roles = "User,Viewer")]        
        [Route("api/{structureId}/PricesGrid/withPlan")]
        public IActionResult GetAllZoneFloorSectionCategsPricesOfEvents(int structureId, string langCode,
       [FromQuery] List<int> listFilieres, [FromQuery] List<int> listOperators, [FromQuery] List<int> listEvents)
        {
            Logger.Debug(structureId, $"GetAllZoneFloorSectionCategsPricesOfEvents({structureId},{string.Join(";", listEvents)})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                List<EventDTO> listRet = _eventManager.GetEventsSessionsZoneFloorSectionCategPrices_full(langCode, structureId, listOperators, listFilieres, listEvents);

                Logger.Debug(structureId, $"GetAllZoneFloorSectionCategsPricesOfEvents({structureId},{string.Join(";", listEvents)})) ok, {listRet.Count} reserves to return");

                return Ok(listRet);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"GetAllPricesOfEvents({structureId},{string.Join(";", listEvents)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// obtenir le plus bas tarif séance par séance parmi les tarifs passés en parametres, filtrés par filiere et operateur (ne tient pas compte des regles de ventes internet)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="filiereId"></param>
        /// <param name="operateurId"></param>
        /// <param name="tarifIdRef"></param>
        /// <param name="listEvents"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<SessionDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 20)]
        [HttpGet]
        // [Authorize(Roles = "User,Viewer")]        
        [Route("api/{structureId}/PricesGrid/GetLower")]
        public IActionResult GetLowerPriceOfEvents(int structureId, string langCode, [FromQuery] List<int> filiereId, [FromQuery] List<int> operateurId, [FromQuery] List<int> tarifIdRef, [FromQuery] List<int> listEvents)
        {
            Logger.Debug(structureId, $"GetLowerPriceOfEvents({structureId},{string.Join(";", listEvents)})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                var lstLowerPrice = _eventManager.GetLowerPriceOfEvents(structureId, langCode, filiereId, operateurId, tarifIdRef, listEvents);

                Logger.Debug(structureId, $"GetLowerPriceOfEvents({structureId},{string.Join(";", listEvents)})) ok, {lstLowerPrice.Count} reserves to return");

                return Ok(lstLowerPrice);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"GetLowerPriceOfEvents({structureId},{string.Join(";", listEvents)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// retourne la liste des reserves communes aux regles de ventes groupés par catégories
        /// Voir sur plan
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="gpIds"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Dictionary<int, List<int>>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 20)]
        [HttpGet]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/LoadReserves/Categories/")]
        public IActionResult LoadCommonsReservesListByCategsId(int structureId, [FromQuery] int[] gpIds)
        {
            Logger.Debug(structureId, $"LoadReserves({structureId},{string.Join(";", gpIds)})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                Dictionary<int, List<int>> listRet = _reservesManager.LoadCommonsReservesListByCategsId(structureId, gpIds.ToList());

                Logger.Debug(structureId, $"LoadReserves({structureId},{string.Join(";", gpIds)})) ok, {listRet.Count} reserves to return");

                return Ok(listRet);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"LoadReserves({structureId},{string.Join(";", gpIds)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// retourne la liste des reserves communes aux regles de ventes
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="gpIds">regles de ventes</param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<ReserveDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 20)]
        [HttpGet]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/LoadReserves/Commons/")]
        public IActionResult LoadCommonsReserves(int structureId, [FromQuery] int[] gpIds)
        {
            Logger.Debug(structureId, $"LoadReserves({structureId},{string.Join(";", gpIds)})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                List<ReserveDTO> listRet = new List<ReserveDTO>();
                List<int> listRsvrIds = _reservesManager.LoadCommonsReservesListForGpIds(structureId, gpIds.ToList());

                var allResvrs = _reservesManager.GetAllReserves(structureId, "");

                foreach (int id in listRsvrIds)
                {
                    listRet.Add(allResvrs.Where(r => r.ReserveId == id).FirstOrDefault());
                }


//                List<ReserveDTO> listRet = _reservesManager.LoadCommonsReservesListForGpIds(structureId, gpIds.ToList());


                Logger.Debug(structureId, $"LoadReserves({structureId},{string.Join(";", gpIds)})) ok, {listRet.Count} reserves to return");

                return Ok(listRet);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"LoadReserves({structureId},{string.Join(";", gpIds)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<CommentaireTarifDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByQueryKeys = new[] { "gpIds" }, Duration = 120)]
        //[ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpGet]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/pricesComments")]
        public IActionResult PricesComments(int structureId, [FromQuery] int[] gpIds)
        {
            Logger.Debug(structureId, $"---- start PricesComments ---- ({structureId},{string.Join(";", gpIds)} )");

            try
            {
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                string commentsPricesListCache = $"comPCache{structureId}_{string.Join(";", gpIds)}";

                List<CommentaireTarifDTO> commentsPricesList = new();
                try
                {
                    _memoryCache.TryGetValue(commentsPricesListCache, out commentsPricesList);

                    if (commentsPricesList == null)
                    {
                        Logger.Debug(structureId, $"commentsPricesList == null");

                        commentsPricesList = _gestionPlaceManager.LoadTarifCommentaires(structureId, gpIds.ToList());
                        Logger.Trace(structureId, $"après LoadTarifCommentaires");
                        if (commentsPricesList.Count > 0)
                        {
                            var cacheExpiryOptions = new MemoryCacheEntryOptions
                            {
                                AbsoluteExpiration = DateTime.Now.AddMinutes(int.Parse(_configuration["Cache:CommentsTarifAbsoluteExpiration"].ToString())),
                                Priority = CacheItemPriority.High,
                                SlidingExpiration = TimeSpan.FromMinutes(int.Parse(_configuration["Cache:CommentsTarifSlidingExpiration"].ToString())),
                            };
                            Logger.Trace(structureId, $"Mise en cache de {commentsPricesList.Count} éléments");

                            _memoryCache.Set(commentsPricesListCache, commentsPricesList, cacheExpiryOptions);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error(structureId, $"PricesComments - impossible de récupérer le cache : commentsPricesList == null : {commentsPricesList == null } nb commentsPricesList : {commentsPricesList.Count} éléments \n {ex.Message} \n {ex.StackTrace}");
                    throw;
                }

                Logger.Debug(structureId, $"return PricesComments({structureId})) ok, nb commentsPricesList : {commentsPricesList.Count} ");

                return Ok(commentsPricesList);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"PricesComments({structureId},{string.Join(";", gpIds)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


    

    }
}
