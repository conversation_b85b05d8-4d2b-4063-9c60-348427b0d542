<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Core.Themis.API.Customers</name>
    </assembly>
    <members>
        <member name="M:Core.Themis.API.Customers.Controllers.CrossSellingController.Get(System.Int32,System.Int32,System.String)">
            <summary>
            Test
            </summary>
            <param name="structureId"></param>
            <param name="basketId"></param>
            <param name="langCode"></param>
            <returns></returns>
        </member>
    </members>
</doc>
