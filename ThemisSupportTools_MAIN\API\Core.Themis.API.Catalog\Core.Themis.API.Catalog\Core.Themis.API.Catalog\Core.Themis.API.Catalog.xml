<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Core.Themis.API.Catalog</name>
    </assembly>
    <members>
        <member name="T:Core.Themis.API.Catalog.Controllers.AccessController">
            <summary>
            points d'acces sur le plan de salle
            </summary>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.AccessController.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Core.Themis.Libraries.Utilities.DB.Interfaces.IWAdmin,Core.Themis.Libraries.Utilities.DB.Interfaces.IOpen,Microsoft.Extensions.Caching.Memory.IMemoryCache,Core.Themis.API.Catalog.Helpers.Interfaces.IApiUtilities)">
            <summary>
            Constructeur
            </summary>
            <param name="configuration"></param>
            <param name="wsAdminSqlConnection"></param>
            <param name="openRod"></param>
            <param name="memoryCache"></param>
            <param name="apiUtilities"></param>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.AccessController.AlotissementsList(System.Int32,System.String)">
            <summary>
            list of all the access 
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.AlotissementsController.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Core.Themis.Libraries.Utilities.DB.Interfaces.IWAdmin,Core.Themis.Libraries.Utilities.DB.Interfaces.IOpen,Microsoft.Extensions.Caching.Memory.IMemoryCache,Core.Themis.API.Catalog.Helpers.Interfaces.IApiUtilities)">
            <summary>
            Constructeur
            </summary>
            <param name="configuration"></param>
            <param name="wsAdminSqlConnection"></param>
            <param name="openRod"></param>
            <param name="memoryCache"></param>
            <param name="apiUtilities"></param>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.AlotissementsController.AlotissementsList(System.Int32,System.String)">
            <summary>
            list of all the alotissements
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.CategsController.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Core.Themis.Libraries.Utilities.DB.Interfaces.IWAdmin,Core.Themis.Libraries.Utilities.DB.Interfaces.IOpen,Microsoft.Extensions.Caching.Memory.IMemoryCache,Core.Themis.API.Catalog.Helpers.Interfaces.IApiUtilities)">
            <summary>
            Constructeur
            </summary>
            <param name="configuration"></param>
            <param name="wsAdminSqlConnection"></param>
            <param name="openRod"></param>
            <param name="memoryCache"></param>
            <param name="apiUtilities"></param>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.CategsController.CategoriesList(System.Int32,System.String)">
            <summary>
            list of all the categories
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.ContingentsController.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Core.Themis.Libraries.Utilities.DB.Interfaces.IWAdmin,Core.Themis.Libraries.Utilities.DB.Interfaces.IOpen,Microsoft.Extensions.Caching.Memory.IMemoryCache,Core.Themis.API.Catalog.Helpers.Interfaces.IApiUtilities)">
            <summary>
            Constructeur
            </summary>
            <param name="configuration"></param>
            <param name="wsAdminSqlConnection"></param>
            <param name="openRod"></param>
            <param name="memoryCache"></param>
            <param name="apiUtilities"></param>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.ContingentsController.ContingentsList(System.Int32,System.String)">
            <summary>
            list of all the contingents
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.EventsController.LoadFuturesEventsSessions(System.Int32,System.String,System.Int32)">
            <summary>
            Ramène les prochaines manifs à venir sur un temps donné
            </summary>
            <param name="structureId"></param>
            <param name="langCode"></param>
            <param name="interval">en minutes, evenements de getdate()-1 heure jusqu'à {interval} minutes</param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.EventsController.LoadEventsSessions(System.Int32,System.String,System.Int32,System.Int32)">
            <summary>
            Renseigne infos de manif et séance 
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.ExportsController.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Core.Themis.Libraries.Utilities.DB.Interfaces.IWAdmin,Core.Themis.Libraries.Utilities.DB.Interfaces.IOpen,Microsoft.Extensions.Caching.Memory.IMemoryCache,Core.Themis.API.Catalog.Helpers.Interfaces.IApiUtilities,Core.Themis.Libraries.BLL.Partners.Interfaces.ISoftwareAGManager)">
            <summary>
            Constructeur
            </summary>
            <param name="configuration"></param>
            <param name="wsAdminSqlConnection"></param>
            <param name="openRod"></param>
            <param name="memoryCache"></param>
            <param name="apiUtilities"></param>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.FloorsController.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Core.Themis.Libraries.Utilities.DB.Interfaces.IWAdmin,Core.Themis.Libraries.Utilities.DB.Interfaces.IOpen,Microsoft.Extensions.Caching.Memory.IMemoryCache,Core.Themis.API.Catalog.Helpers.Interfaces.IApiUtilities)">
            <summary>
            Constructeur
            </summary>
            <param name="configuration"></param>
            <param name="wsAdminSqlConnection"></param>
            <param name="openRod"></param>
            <param name="memoryCache"></param>
            <param name="apiUtilities"></param>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.FloorsController.ZonesList(System.Int32,System.String)">
            <summary>
            list of all the zones
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.GateController.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Core.Themis.Libraries.Utilities.DB.Interfaces.IWAdmin,Core.Themis.Libraries.Utilities.DB.Interfaces.IOpen,Microsoft.Extensions.Caching.Memory.IMemoryCache,Core.Themis.API.Catalog.Helpers.Interfaces.IApiUtilities)">
            <summary>
            Constructeur
            </summary>
            <param name="configuration"></param>
            <param name="wsAdminSqlConnection"></param>
            <param name="openRod"></param>
            <param name="memoryCache"></param>
            <param name="apiUtilities"></param>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.GateController.GatesList(System.Int32,System.String)">
            <summary>
            list of all the alotissements
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.ReservesController.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Core.Themis.Libraries.Utilities.DB.Interfaces.IWAdmin,Core.Themis.Libraries.Utilities.DB.Interfaces.IOpen,Microsoft.Extensions.Caching.Memory.IMemoryCache,Core.Themis.API.Catalog.Helpers.Interfaces.IApiUtilities)">
            <summary>
            Constructeur
            </summary>
            <param name="configuration"></param>
            <param name="wsAdminSqlConnection"></param>
            <param name="openRod"></param>
            <param name="memoryCache"></param>
            <param name="apiUtilities"></param>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.ReservesController.ReservesList(System.Int32,System.String)">
            <summary>
            list of all the reserves
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.SeatsController.SeatsList(System.Int32,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            liste des sieges sur une seule zone, etage, section
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <param name="eventId"></param>
            <param name="sessionId"></param>
            <param name="zoneId"></param>
            <param name="floorId"></param>
            <param name="sectionId"></param>
            <param name="webUserId"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.SeatsController.SeatsListIndispo(System.Int32,System.String,System.Int32,System.Int32,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.Int32})">
            <summary>
            liste des sieges indispo (etat X ou I) sur plusieures zones, etages, sections
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <param name="eventId"></param>
            <param name="sessionId"></param>
            <param name="listZonesId"></param>
            <param name="listFloorsId"></param>
            <param name="listSectionsId"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.SeatsController.SeatsList(System.Int32,System.String,System.Int32,System.Int32,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.Int32},System.Int32)">
            <summary>
            liste des sieges sur plusieures zones, etages, sections
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <param name="eventId"></param>
            <param name="sessionId"></param>
            <param name="listZonesId"></param>
            <param name="listFloorsId"></param>
            <param name="listSectionsId"></param>
            <param name="webUserId"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.SeatsController.TextsList(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            les textes, textes longs, lignes et poteaux
            </summary>
            <param name="structureId"></param>
            <param name="eventId"></param>
            <param name="sessionId"></param>
            <param name="listFloorsId"></param>
            <param name="floorId"></param>
            <param name="sectionId"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.SeatsController.TextsList(System.Int32,System.Int32,System.Int32,System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.Int32},System.Collections.Generic.List{System.Int32})">
            <summary>
            les textes, textes longs, lignes et poteaux sur plusieurs zones, etages, sections
            </summary>
            <param name="structureId"></param>
            <param name="eventId"></param>
            <param name="sessionId"></param>
            <param name="listZonesId"></param>
            <param name="listFloorsId"></param>
            <param name="listSectionsId"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.SectionsController.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Core.Themis.Libraries.Utilities.DB.Interfaces.IWAdmin,Core.Themis.Libraries.Utilities.DB.Interfaces.IOpen,Microsoft.Extensions.Caching.Memory.IMemoryCache,Core.Themis.API.Catalog.Helpers.Interfaces.IApiUtilities)">
            <summary>
            Constructeur
            </summary>
            <param name="configuration"></param>
            <param name="wsAdminSqlConnection"></param>
            <param name="openRod"></param>
            <param name="memoryCache"></param>
            <param name="apiUtilities"></param>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.SectionsController.SectionsList(System.Int32,System.String)">
            <summary>
            list of all the zones
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.TranslationsController.TranslationAreas(System.Int32)">
            <summary>
            list of all the sections
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns>retourne la liste des sections (traductions)</returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.TranslationsController.TranslationVariables(System.Int32)">
            <summary>
            list of all the variables (traductions)
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns>retourne la liste des sections (traductions)</returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.TranslationsController.TranslationFieldsCode(System.Int32)">
            <summary>
            list of all the sections
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns>retourne la liste des sections (traductions)</returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.TribuneController.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Core.Themis.Libraries.Utilities.DB.Interfaces.IWAdmin,Core.Themis.Libraries.Utilities.DB.Interfaces.IOpen,Microsoft.Extensions.Caching.Memory.IMemoryCache,Core.Themis.API.Catalog.Helpers.Interfaces.IApiUtilities)">
            <summary>
            Constructeur
            </summary>
            <param name="configuration"></param>
            <param name="wsAdminSqlConnection"></param>
            <param name="openRod"></param>
            <param name="memoryCache"></param>
            <param name="apiUtilities"></param>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.TribuneController.GatesList(System.Int32,System.String)">
            <summary>
            list of all the alotissements
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.ZonesController.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Core.Themis.Libraries.Utilities.DB.Interfaces.IWAdmin,Core.Themis.Libraries.Utilities.DB.Interfaces.IOpen,Microsoft.Extensions.Caching.Memory.IMemoryCache,Core.Themis.API.Catalog.Helpers.Interfaces.IApiUtilities)">
            <summary>
            Constructeur
            </summary>
            <param name="configuration"></param>
            <param name="wsAdminSqlConnection"></param>
            <param name="openRod"></param>
            <param name="memoryCache"></param>
            <param name="apiUtilities"></param>
        </member>
        <member name="M:Core.Themis.API.Catalog.Controllers.ZonesController.ZonesList(System.Int32,System.String)">
            <summary>
            list of all the zones
            </summary>
            <param name="structureId"></param>
            <param name="codeLang"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Helpers.ApiUtilities.GetPartnerIdAndRoles(System.String)">
            <summary>
            obtenir le partenaire et ses infos et roles à partir du nom, avec gestion d'un cache 
            </summary>
            <param name="partnerName">code partenaire</param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Helpers.ApiUtilities.GetPartnerIdAndRoles(System.Int32)">
            <summary>
            obtenir le partenaire et ses infos et roles à partir de l'id, avec gestion d'un cache 
            </summary>
            <param name="partnerId">partenaire id</param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Helpers.ApiUtilities.GetConnexionOpen(System.Int32,System.String)">
            <summary>
            get connexion Open à partir du cache eventuel
            </summary>
            <param name="structureId"></param>
            <param name="typeRun"></param>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.Helpers.ApiUtilities.GetConnexionWebLibrary">
            <summary>
            Get connexion WebLibraryDB
            </summary>
            <returns></returns>
        </member>
        <member name="M:Core.Themis.API.Catalog.PartnerRightsCheckeraaa.PartnerHasRight(System.Int32,Microsoft.Extensions.Primitives.StringValues)">
            <summary>
            verifier si le token contient la structure
            </summary>
            <param name="structureId"></param>
            <returns></returns>
        </member>
    </members>
</doc>
