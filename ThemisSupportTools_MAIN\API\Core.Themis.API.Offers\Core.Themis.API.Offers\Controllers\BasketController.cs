﻿using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace Core.Themis.API.Offers.Controllers
{

    [ApiController]
    public class BasketController : ControllerBase
    {
        private readonly IConfiguration Configuration;
        private readonly IBasketManager _basketManager;

        private static readonly RodrigueNLogger Logger = new();


        public BasketController(
            IConfiguration configuration, 
            IBasketManager basketManager)
        {
            Configuration = configuration;
            _basketManager = basketManager;
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<BasketDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 1)]
        [HttpGet]
       // [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/Basket/Open/{basketId}/{langCode}")]

        public IActionResult GetBasketFromOpen(int structureId, int basketId, string langCode)
        {
            Logger.Debug(structureId, $"GetBasketFromOpen({structureId},{basketId})...");


            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            BasketDTO baskToRet = _basketManager.GetAllBasketInfo(structureId, basketId, 0).FirstOrDefault();

            _basketManager.FillFromOpen(structureId, baskToRet, langCode);

            return Ok(baskToRet);
        }


        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<BasketDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 1)]
        [HttpPut]        
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/Basket/Transform/{basketId}/{langCode}")]

        public IActionResult PanierTransformator(int structureId, int basketId, string langCode)
        {
            Logger.Debug(structureId, $"PanierTransformator({structureId},{basketId})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            BasketDTO baskToRet = _basketManager.BasketTransformator(structureId, basketId, langCode);

            return Ok(baskToRet);
        }



    }
}
