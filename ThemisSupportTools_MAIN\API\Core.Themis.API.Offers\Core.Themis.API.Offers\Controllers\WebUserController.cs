﻿using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.WTObjects;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Themis.API.Offers.Controllers
{

    [ApiController]
    public class WebUserController : ControllerBase
    {
        private readonly IConfiguration Configuration;
        private readonly IWebUserManager _webUserManager;
        private readonly IBuyerProfilManager _buyerProfilManager;
        private readonly IGestionTraceManager _gestionTraceManager;
        private static readonly RodrigueNLogger Logger = new();

        public WebUserController(
            IConfiguration configuration,
            IWebUserManager webUserManager,
            IGestionTraceManager gestionTraceManager)
        {
            Configuration = configuration;
            _webUserManager = webUserManager;
            _gestionTraceManager = gestionTraceManager;
        }

        /// <summary>
        /// creation d'un web user (= internaute)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="webUser"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="bpLogin"></param>
        /// <param name="bpPassword"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<WebUser>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 1)]
        [HttpPost]
        //[Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/WebUser")]
        [Route("api/{structureId}/WebUser/{buyerProfilId}")]
        [Route("api/{structureId}/WebUser/{bpLogin}/{bpPassword}")]

        public IActionResult CreateWebUser(int structureId, WebUser webUser, int buyerProfilId, string bpLogin, string bpPassword)
        {
            Logger.Debug(structureId, $"CreateWebUser({structureId})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            int myBprofilId = 0;
            if (!string.IsNullOrWhiteSpace(bpLogin) || !string.IsNullOrWhiteSpace(bpPassword) || buyerProfilId != 0)
            {
                BuyerProfilDTO buyerProfil = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, buyerProfilId, bpLogin, bpPassword);
                if (buyerProfil != null)
                {
                    myBprofilId = buyerProfil.Id;
                    if (buyerProfil.IsReseller)
                    {
                        webUser.IdentiteId = buyerProfil.IdentityPaiement;
                    }
                }
                else
                {
                    var pb = Problem($"can't retrieve buyer profil", null, StatusCodes.Status401Unauthorized);
                    return pb;
                }
            }
            webUser.ProfilAcheteurId = myBprofilId;
            
            WebUser newwebUser = _webUserManager.SetWT(structureId, webUser);

            return Ok(newwebUser);
        }


        /// <summary>
        /// Met à jour le code à usage unique
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="webUserId"></param>
        /// <param name="codeUsageUnique"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(bool))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [HttpPut]
        [Route("api/{structureId}/webuser/{webUerId}/{codeUsageUnique}")]
        public async Task<IActionResult> SetCodeUsageUniqueAsync(int structureId, int webUserId, string codeUsageUnique)
        {
            //Cette méthode a été créée pour l'appeler depuis le site INDIV "fpaniersWidget" 
            //mais n'est pas appelée car le panier est mis à jour en direct dans l'Indiv (comme pour le fpanierS)


            Logger.Debug(structureId, $"SetCodeUsageUniqueAsync({structureId}, {webUserId}, {codeUsageUnique})");

            try
            {
                var result = await _webUserManager.SetCodeUsageUniqueAsync(structureId, webUserId, codeUsageUnique);

                return Ok(result);
            }
            catch (Exception ex)
            {
                string errorMessage = $"SetCodeUsageUniqueAsync({structureId}, {webUserId}, {codeUsageUnique}) {ex.Message}";
                Logger.Error(structureId, errorMessage);
                _gestionTraceManager.WriteLogErrorMessage(structureId, webUserId, errorMessage);

                throw;
            }

        }
    }
}
