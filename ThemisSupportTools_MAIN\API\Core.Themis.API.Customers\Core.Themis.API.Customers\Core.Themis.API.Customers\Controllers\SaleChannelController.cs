﻿using Core.Themis.Libraries.BLL.Managers.Sales.Interfaces;
using Core.Themis.Libraries.Utilities.Crypto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using System;
using System.Net;

// For more information on enabling Web API for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860

namespace Core.Themis.API.Customers.Controllers
{
    [Authorize(Roles = "Admin, Viewer, User")]
   // [Route("api/[controller]")]
    [ApiController]
    public class SaleChannelController : ControllerBase
    {
        private readonly ISaleChannelManager _saleChannelManager;

        public SaleChannelController(ISaleChannelManager saleChannelManager)
        {
            _saleChannelManager = saleChannelManager;
        }

        /// <summary>
        /// Liste des filières de ventes pour les avis
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("api/{structureId}/opinionSaleChannel")]
        public IActionResult LoadOpinionSaleChannel(int structureId)
        {
            try
            {
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                return Ok(_saleChannelManager.LoadOpinionSaleChannelList(structureId));
            }
            catch (Exception ex)
            {
                return Problem(ex.Message, null, (int)HttpStatusCode.InternalServerError);
            }
        }
    }
}
