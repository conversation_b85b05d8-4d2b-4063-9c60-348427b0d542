﻿using AutoMapper;
using Core.Themis.Api.External.Helpers.Interfaces;
using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using RestSharp;
using System.Net;
using static Dapper.SqlMapper;

namespace Core.Themis.Api.External.Controllers
{
    /// <summary>
    /// Events and their sessions
    /// </summary>
    [ApiController]
    [ApiExplorerSettings(GroupName = "ext")]
    public class EventsController : ControllerBase
    {
        private static readonly RodrigueNLogger Logger = new RodrigueNLogger();
        private readonly IApiOffersClient _apiOffersClients;
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IEventManager _eventManager;
        private readonly IBuyerProfilManager _buyerProfilManager;

        private IMapper Mapper
        {
            get;
        }

        public EventsController(IConfiguration configuration, IMemoryCache memoryCache, IMapper mapper, IApiOffersClient apiOffersClient, IEventManager eventManager, IBuyerProfilManager buyerProfilManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            this.Mapper = mapper;
            _apiOffersClients = apiOffersClient;
            _eventManager = eventManager;
            _buyerProfilManager = buyerProfilManager;

        }

        /// <summary>
        /// Get schedule for the buyer profil
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <param name="bpLogin">The buyer profil login</param>
        /// <param name="bpPassword">The buyer profil password</param>
        /// <returns>List of events with sessions</returns>
        /// <remarks>
        /// Returns the list of events with sessions for the buyer profil
        /// 
        /// Example of request: 
        ///
        ///     GET api/987/Schedule/fr/BP/PassW
        /// </remarks>
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Event>))]
        [HttpGet]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/Schedule/{langCode}/{bpLogin}/{bpPassword}")]
        public IActionResult Schedule(int structureId, string langCode, string bpLogin, string bpPassword)
        {
            Logger.Debug(structureId, $"Schedule({structureId},{langCode},{bpLogin},{bpPassword})...");

            var accT = Request.Headers[HeaderNames.Authorization];

            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            BuyerProfilDTO bp = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, 0, bpLogin, bpPassword);



            if (bp == null || bp.Id == 0)
            {
                string msgErr = $"Schedule({structureId}) buyer profil does not exists";
                Logger.Error(structureId, msgErr);
                var pb = Problem(msgErr, null, StatusCodes.Status404NotFound);

                return pb;
            }

            //bp = _buyerProfilRepository.Get(structureId, (int)bp.id);
            if (!BuyerProfilManager.BuyerProfil_IsCorrect(bp))
            {
                string msgErr = $"Schedule({structureId}) buyer profil {bp.Id} configuration is not correct";
                Logger.Error(structureId, msgErr);
                var pb = Problem(msgErr, null, StatusCodes.Status406NotAcceptable);

                return pb;
            }

            List<EventDTO> listRet = _eventManager.GetSchedule(langCode, structureId, 0, bp.Id);

            List<Event> lev = (List<Event>)Mapper.Map(listRet, typeof(List<EventDTO>), typeof(List<Event>));

            return Ok(lev);
        }

        /// <summary>
        /// Get schedule for the customer
        /// </summary>
        /// <param name="structureId">The Unique structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <param name="identiteId">The unique customer identifier (opt)</param>
        /// <return> Events and sessions</return>>
        /// <remarks>
        /// Returns the list of events with sessions 
        /// 
        /// Example of request:
        ///
        ///     GET api/987/Schedule/fr/123
        /// </remarks>
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Event>))]
        [HttpGet]
        [Authorize(Roles = "Integrateur,Admin, Viewer")]
        [Route("api/{structureId}/Schedule/{langCode}/{identiteId}")]
        public IActionResult Schedule(int structureId, string langCode, int identiteId)
        {
            Logger.Debug(structureId, $"Schedule({structureId},{langCode},{identiteId}...");

            var accT = Request.Headers[HeaderNames.Authorization];

            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            List<EventDTO> listRet = _eventManager.GetSchedule(langCode, structureId, identiteId, 0);

            List<Event> lev = (List<Event>)Mapper.Map(listRet, typeof(List<EventDTO>), typeof(List<Event>));

            return Ok(lev);

        }

        /// <summary>
        /// Get general audience schedule
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>  
        /// <returns>List of events with sessions</returns>
        /// <remarks>
        /// Returns a list of events with sessions accessible for anyone
        /// 
        /// Example of request:
        /// 
        ///     GET api/987/Schedule/fr
        /// </remarks>
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Event>))]
        [HttpGet]
        [Route("api/{structureId}/Schedule/{langCode}")]
        public IActionResult ScheduleTTPublic(int structureId, string langCode)
        {
            Logger.Debug(structureId, $"Schedule tt public ({structureId},{langCode})...");

            var accT = Request.Headers[HeaderNames.Authorization];

            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            List<EventDTO> listRet = _eventManager.GetSchedule(langCode, structureId, 0, 0);

            List<Event> lev = (List<Event>)Mapper.Map(listRet, typeof(List<EventDTO>), typeof(List<Event>));

            return Ok(lev);
        }

        /// <summary>
        /// Get general audience schedule
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param> 
        /// <returns>List of events with sessions</returns>
        /// <remarks>
        /// Returns a list of available events with sessions accessible for anyone
        /// 
        /// Example of request:
        ///
        ///     GET api/987/Schedule/fr/dispo
        /// </remarks>
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Event>))]
        [HttpGet]
        [Route("api/{structureId}/Schedule/{langCode}/dispo")]
        public IActionResult ScheduleTTPublicDispo(int structureId, string langCode)
        {
            Logger.Debug(structureId, $"Schedule tt public avec dispo({structureId},{langCode})...");

            var accT = Request.Headers[HeaderNames.Authorization];

            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            List<EventDTO> listRet = _eventManager.GetSchedule(langCode, structureId, 0, 0, true);

            List<Event> lev = (List<Event>)Mapper.Map(listRet, typeof(List<EventDTO>), typeof(List<Event>));

            return Ok(lev);
        }


        /// <summary>
        /// Get full schedule, prices do not matter
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <param name="interval"></param>
        /// <remarks>
        /// Returns the complete schedule regardless of the prices.
        /// 
        /// Examples of requests:
        /// 
        /// Returns all events/sessions from now to now + 10 years:
        /// 
        ///     GET /Events/Sessions/fr
        ///     {
        ///     }
        ///     
        ///  OR
        /// Returns all events/sessions between 2020-06-11 and 2030-06-11:
        /// 
        ///     GET /Events/Sessions/fr
        ///     {
        ///       "pStartDate": "2020-06-11T12:24:56.931Z",
        ///       "pEndDate": "2030-06-11T12:24:56.931Z"
        ///     }
        ///     
        ///  OR
        ///  Returns all events/sessions between 2020-06-11 and now + 10 years:
        ///  
        ///     GET /Events/Sessions/fr
        ///     {
        ///       "pStartDate": "2020-06-11T12:24:56.931Z"
        ///     }
        ///     
        ///  OR
        ///  Returns all events/sessions between now and 2025-06-11:  
        ///  
        ///     GET /Events/Sessions/fr
        ///     {
        ///       "pEndDate": "2025-06-11T12:24:56.931Z"
        ///     }        
        ///
        /// </remarks>
        //[HttpGet]
        //[Produces("application/json")]
        //[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Event>))]
        //[Route("api/{structureId}/Events/Sessions/{langCode}/body")]

        //public IActionResult EventsFuturesBody(int structureId, string langCode, [FromBody] IntervalDatesModel interval)
        //{
        //    Logger.Debug(structureId, $"EventsFuturesBody({structureId},{langCode}...");

        //    var accT = Request.Headers[HeaderNames.Authorization];

        //    if (!TokenManager.PartnerHasRight(structureId, accT))
        //    {
        //        //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
        //        var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
        //        return pb;
        //    }

        //    if (interval.pEndDate == default)
        //    {
        //        interval.pEndDate = DateTime.Now.AddYears(20);
        //    }
        //    if (interval.pStartDate == default)
        //    {
        //        interval.pStartDate = DateTime.Now;
        //    }

        //    TimeSpan diff = interval.pEndDate - DateTime.Now;
        //    var toMinutes = Convert.ToInt32(diff.TotalMinutes);

        //    diff = DateTime.Now - interval.pStartDate;
        //    var fromMinutes = Convert.ToInt32(diff.TotalMinutes);

        //    List<EventDTO> listRet = _eventManager.LoadEventsSessionsInterval(structureId, fromMinutes, toMinutes, langCode);

        //    List<Event> lev = (List<Event>)Mapper.Map(listRet, typeof(List<EventDTO>), typeof(List<Event>));

        //    return Ok(lev);

        //}

        /// <summary>
        /// Get full schedule, prices do not matter
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <param name="startDate">The starting date of the session</param>
        /// <param name="endDate">The end date of the session</param>
        /// <returns>Full schedule</returns>
        /// <remarks>
        /// Returns the complete schedule regardless of the prices.
        /// 
        /// Examples of requests:
        /// 
        ///     curl -X 'GET' \
        ///      ' [url] /api/987/Events/Sessions/fr?startDate=2024-06-11&amp;endDate=2025-06-11' \
        ///      -H 'accept: application/json' \
        ///      -H 'Authorization: Bearer eyJhbGciOiJIU_xxxxxxxxx_nFfgkT1L7DhzYO5bSsBxyPBVY'
        /// 
        /// Sample parameters:
        /// 
        /// Returns all events/sessions from now to now + 10 years:
        /// 
        ///     GET /Events/Sessions/fr
        ///     
        ///  OR
        /// Returns all events/sessions between 2020-06-11 and 2025-06-11:
        /// 
        ///     GET /Events/Sessions/fr?startDate=2020-06-11TT12%3A00%3A00Z&amp;endDate=2025-06-11TT12%3A00%3A00Z
        ///     
        ///  OR
        ///  Returns all events/sessions between 2020-06-11 and now + 10 years:
        ///  
        ///     GET /Events/Sessions/fr?startDate=2020-06-11T12%3A00%3A00Z 
        ///     
        ///  OR
        ///  Returns all events/sessions between now and 2025-06-11 12:00:00:  
        ///  
        ///     GET /Events/Sessions/fr?endDate=2025-06-11T12%3A00%3A00Z 
        ///
        /// </remarks>

        [HttpGet]
        [Produces("application/json"), Authorize]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Event>))]
        [Route("api/{structureId}/Events/Sessions/{langCode}")]
        [ResponseCache(Duration = 30)]
        public async Task<IActionResult> EventsFuturesAsync(int structureId, string langCode, [FromQuery] DateTime startDate, [FromQuery] DateTime endDate)
        {
            Logger.Debug(structureId, $"EventsFutures({structureId},{langCode},{startDate},{endDate}...");

            var accT = Request.Headers[HeaderNames.Authorization];

            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            if (endDate == default)
            {
                endDate = DateTime.Now.AddYears(20);
            }
            if (startDate == default)
            {
                startDate = DateTime.Now;
            }


            TimeSpan diff = endDate - DateTime.Now;
            var toMinutes = Convert.ToInt32(diff.TotalMinutes);

            diff = DateTime.Now - startDate;
            var fromMinutes = Convert.ToInt32(diff.TotalMinutes);

            List<EventDTO> listRet = await _eventManager.LoadEventsSessionsIntervalAsync(structureId, fromMinutes, toMinutes, langCode);

            List<Event> lev = (List<Event>)Mapper.Map(listRet, typeof(List<EventDTO>), typeof(List<Event>));

            return Ok(lev);
        }


        /// <summary>
        /// Get images of event (2 images)
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="eventId">The event identifier</param>
        /// <returns>List of events with images</returns>
        /// <remarks>
        /// Returns the choosen event with 2 images 
        /// 
        /// Examples of requests:
        /// 
        ///     GET api/987/Events/123/images
        ///     GET api/987/Events/images
        ///     
        /// Response body JSON:
        /// 
        ///     {
        ///         "id": 123,
        ///         "image1": "",
        ///         "image2": ""
        ///     }
        /// </remarks>
        /// <response code="200">Success</response>

        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<EventImages>))]
        [HttpGet]
        [Route("api/{structureId}/Events/{eventId}/images")]
        [Route("api/{structureId}/Events/images")]
        public IActionResult Images(int structureId, int eventId = 0)
        {
            Logger.Debug(structureId, $"EventsImages({structureId},{eventId})...");

            var accT = Request.Headers[HeaderNames.Authorization];

            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                return pb;
            }

            Task<List<EventDTO>> listEvents = _eventManager.GetImagesAsync(structureId, eventId);

            List<EventImages> lev = (List<EventImages>)Mapper.Map(listEvents.Result, typeof(List<EventDTO>), typeof(List<EventImages>));

            return Ok(lev);
        }

    }
}
