﻿using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Products.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.Products;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Core.Themis.API.Offers.Controllers
{
    [ApiController]
    public class ProductsController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IProductManager _productManager;
        private readonly IBasketManager _basketManager;

        private static readonly RodrigueNLogger Logger = new();

        public ProductsController(
            IConfiguration configuration,
            IBasketManager basketManager,
            IProductManager productManager)
        {
            _configuration = configuration;
            _productManager = productManager;
            _basketManager = basketManager;
        }


        /// <summary>
        /// produits mode d'obtention COMMUNS à tous les gestion places
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="basketid"></param>
        /// <param name="filiereId"></param>

        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<ProductDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpPost]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/pushfraisdossier/{basketid}/{filiereId}/{langCode}")]
        public IActionResult PushProductsFraisDossier(int structureId, int basketid, int filiereId, string langCode)
        {
            try
            {
                Logger.Debug(structureId, $"LoadProductsFraisDossier({structureId},{basketid},{filiereId},{langCode})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                List<ProductDTO> listRet = new();
                listRet = _productManager.LoadFraisDossier(structureId, filiereId, langCode);

                if (listRet != null)
                {
                    bool ret = _basketManager.DelFraisDossier(structureId, basketid);
                    bool ret2 = _basketManager.AddFraisDossier(structureId, basketid, listRet);
                }

                return Ok(listRet);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"LoadProductsFraisDossier({structureId},{basketid},{filiereId},{langCode}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }



        /// <summary>
        /// produits mode d'obtention COMMUNS à tous les gestion places, et mode d'obtentions COMMUNS aux produits
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="gpIds">liste des gps id du panier</param>
        /// <param name="prodsId">liste des produits id du panier</param>    
        /// <param name="prodsJustifId">liste des produits "à justif" = qui s'excluent s'ils sont avec d'autres</param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<ProductDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 30)]
        [HttpGet]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/loadmo/{langCode}")]
        public IActionResult LoadMO(int structureId, string langCode,
            int basketId,
            int buyerProfilId,
            [FromQuery] int[] gpIds,
            [FromQuery] int[] prodsId,
            [FromQuery] int[] prodsJustifId
            )
        {
            try
            {
                Logger.Debug(structureId, $"LoadMO({structureId},)...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);



                var thisbask = _basketManager.GetAllBasketInfo(structureId, basketId, 0).FirstOrDefault();


                List<ProductDTO> listReturn = _productManager.GetAllObtainingModeForProducts(structureId, prodsJustifId.ToList(),  langCode, thisbask, buyerProfilId, ListGpIds: gpIds.ToList(), listProds: prodsId.ToList());
                Logger.Debug(structureId, $"LoadMO({structureId}) ok, 0 products to return");

                return Ok(listReturn);
            }

            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"LoadMO({structureId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// maquettes de TOUS les produits pour tel mode d'obtention
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="moId"></param>

        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Dictionary<int, int>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(Duration = 600)]
        [HttpGet]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/loadproductsmaquette/{moId}")]
        public IActionResult LoadProductsMaquette(int structureId, int moId)
        {
            try
            {
                Logger.Debug(structureId, $"LoadProductsMaquette({structureId},)...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                Dictionary<int, int> listRet = new Dictionary<int, int>();
                listRet = _productManager.LoadMaquettes(structureId, moId);

                Logger.Debug(structureId, $"LoadProductsMaquette({structureId}) ok, 0 events to return");

                return Ok(listRet);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"LoadProductsMaquette({structureId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Dictionary<int, int>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(Duration = 600)]
        [HttpDelete]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/deleteproduct/{basketId}")]
        public IActionResult DeleteProduct(int structureId, int basketId, [FromQuery] List<int> basketProductsIds)
        {
            try
            {
                Logger.Debug(structureId, $"---- start DeleteProduct({structureId}, {basketId}, {string.Join(",", basketProductsIds)})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                try
                {
                    if (basketProductsIds.Count > 0)
                    {
                        var isProductDeleted = _basketManager.DeleteProduct(structureId, basketId, basketProductsIds);

                        Logger.Debug(structureId, $"DeleteProduct({structureId}) {isProductDeleted}");
                        return Ok(isProductDeleted);
                    }
                    else
                    {
                        Logger.Error(structureId, $"DeleteProduct liste de produit est vide");
                        return Problem();
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error(structureId, ex, $"DeleteProduct ({structureId}, {basketId}, {string.Join(",", basketProductsIds)} )");
                    throw;
                }
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"DeleteProduct({structureId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// produit liés à la manif
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="ListEventsId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(EventDTO))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(Duration = 2)]
        [HttpGet]
        // [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/ProductsOfEvents/{langCode}")]
        public IActionResult LoadProductsOfManif(int structureId, string langCode, [FromQuery] List<int> ListEventsId)
        {
            try
            {
                Logger.Debug(structureId, $"---- start LoadProductsOfManif({structureId} {langCode} {string.Join(",", ListEventsId)})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                try
                {
                    List<EventDTO> listEventsReturn = _productManager.LoadProductsIntoEvents(structureId, langCode, ListEventsId);

                    //foreach (int eventId in ListEventsId)
                    //{
                    //    EventDTO evt = new()
                    //    {
                    //        EventId = eventId,
                    //        ProductsBoutique = new()
                    //        {
                    //            ProductFamiliesList = new()
                    //        }
                    //    };

                    //    evt.ProductsBoutique.ProductFamiliesList = _productManager.LoadProductsEvent(structureId, langCode, eventId);
                    //    listEventsReturn.Add(evt);
                    //}
                    return Ok(listEventsReturn);
                }
                catch (Exception ex)
                {
                    Logger.Error(structureId, ex, $"LoadProductsOfManif ({structureId} {langCode} {string.Join(",", ListEventsId)})");
                    throw;
                }
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"LoadProductsOfManif({structureId} {langCode} {string.Join(",", ListEventsId)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
        /// <summary>
        /// produit liés aux sessions
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="ListSessionsId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(SessionDTO))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(Duration = 2)]
        [HttpGet]
        // [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/ProductsOfSessions/{langCode}")]
        public IActionResult LoadProductsOfSession(int structureId,
            string langCode,
            [FromQuery] List<int> ListSessionsId)

        {
            try
            {
                Logger.Debug(structureId, $"---- start ProductsOfSession({structureId} {langCode} {string.Join(",", ListSessionsId)})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                try
                {
                    List<SessionDTO> listSessionReturn = _productManager.LoadProductsIntoSessions(structureId, langCode, ListSessionsId);


                    //foreach (int sessionId in ListSessionsId)
                    //{
                    //    SessionDTO ses = new()
                    //    {
                    //        SessionId = sessionId,
                    //        ProductsBoutique = new()
                    //        {
                    //            ProductFamiliesList = new()
                    //        }
                    //    };
                    //    ses.ProductsBoutique.ProductFamiliesList = _productManager.LoadProductsSession(structureId, langCode, sessionId);
                    //    listSessionReturn.Add(ses);
                    //}
                    return Ok(listSessionReturn);
                }
                catch (Exception ex)
                {
                    Logger.Error(structureId, ex, $"ProductsOfSession ({structureId} {langCode} {string.Join(",", ListSessionsId)})");
                    throw;
                }
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"ProductsOfSession({structureId} {langCode} {string.Join(",", ListSessionsId)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// produit liés au panier
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="ListSessionsId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<ProductFamilyDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 120)]
        [HttpGet]
        // [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/ProductsGlobalBasket/{langCode}")]
        public IActionResult LoadProductsGlobalBasket(int structureId,
            string langCode)
        {
            try
            {
                Logger.Debug(structureId, $"---- start LoadProductsGlobalBasket({structureId} {langCode})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                try
                {
                    List<ProductFamilyDTO> list = _productManager.LoadProductsGlobauxBasket(structureId, langCode);

                    return Ok(list);
                }
                catch (Exception ex)
                {
                    Logger.Error(structureId, ex, $"LoadProductsGlobalBasket ({structureId}, {langCode})");
                    throw;
                }
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"LoadProductsGlobalBasket({structureId}, {langCode}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// tous les produits, une ligne par stock
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="FamilyId"></param>
        /// <param name="SubFamilyId"></param>
        /// <param name="listProducts"></param>
        /// <param name="SearchTerm"></param>
        /// <param name="PageIndex"></param>
        /// <param name="ItemsPerPage"></param>
        /// <param name="TopProducts"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<ProductDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(Duration = 20)]
        [HttpGet]
        // [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/Products/{langCode}")]

        public IActionResult LoadAllProducts(int structureId,
         string langCode,
            [FromQuery] int FamilyId,
            [FromQuery] int SubFamilyId,
            [FromQuery] List<int> listProducts,
            [FromQuery] string SearchTerm,
            [FromQuery] int PageIndex,
            [FromQuery] int ItemsPerPage,
            [FromQuery] int TopProducts)
        {
            try
            {
                Logger.Debug(structureId, $"---- start LoadAllProducts({structureId}, {langCode})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                try
                {
                    List<ProductDTO> l = _productManager.LoadProducts(structureId, langCode, FamilyId, SubFamilyId, listProducts, SearchTerm, PageIndex, ItemsPerPage, TopProducts);

                    return Ok(l);
                }
                catch (Exception ex)
                {
                    Logger.Error(structureId, ex, $"LoadAllProducts ({structureId} )");
                    throw;
                }
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"LoadAllProducts({structureId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }



        /// <summary>
        /// produit boutique
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="FamilyId"></param>
        /// <param name="SubFamilyId"></param>
        /// <param name="ProductId"></param>
        /// <param name="SearchTerm"></param>
        /// <param name="PageIndex"></param>
        /// <param name="ItemsPerPage"></param>
        /// <param name="TopProducts"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(BoutiqueDTO))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(Duration = 2)]
        [HttpGet]
        // [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/Products/boutique/{langCode}")]
        public IActionResult LoadProductsOfBoutique(int structureId,
        string langCode,
        [FromQuery] int FamilyId,
        [FromQuery] int SubFamilyId,
        [FromQuery] int ProductId,
        [FromQuery] string SearchTerm,
        [FromQuery] int PageIndex,
        [FromQuery] int ItemsPerPage,
        [FromQuery] int TopProducts)
        {
            try
            {
                Logger.Debug(structureId, $"---- start LoadProductsOfBoutique({structureId}, {langCode})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);


                var bouqReturn = _productManager.LoadProductsOfBoutique(structureId, langCode, SearchTerm, ItemsPerPage, PageIndex, TopProducts, FamilyId, SubFamilyId, ProductId);

                return Ok(bouqReturn);

            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"LoadProductsOfBoutique({structureId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
    }
}
