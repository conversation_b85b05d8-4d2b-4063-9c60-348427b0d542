﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Infrastructure;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Core.Themis.API.Customers.Handlers
{
    public class RolesAuthorizationHandler : AuthorizationHandler<RolesAuthorizationRequirement>, IAuthorizationHandler
    {
        private static readonly NLog.Logger Logger = NLog.LogManager.GetCurrentClassLogger();

        List<string> listRolesDuToken = new List<string>();
        protected override Task HandleRequirementAsync(AuthorizationHandlerContext context,
                                                       RolesAuthorizationRequirement requirement)
        {

            List<string> listRolesDuToken = new List<string>();
            IEnumerable<string> rolesDemandes = new List<string>();

            if (context.User == null || !context.User.Identity.IsAuthenticated)
            {
                Logger.Error("token absent ou invalide");

                context.Fail();
                return Task.CompletedTask;
            }

            var validRole = false;
            if (requirement.AllowedRoles == null ||
                requirement.AllowedRoles.Any() == false)
            {
                validRole = true;
            }
            else
            {
                listRolesDuToken = context.User.Claims.Where(m => m.Type == ClaimTypes.Role).Select(c => c.Value).ToList();

                var claims = context.User.Claims;
                //var userName = claims.FirstOrDefault(c => c.Type == "name").Value;
                rolesDemandes = requirement.AllowedRoles;

                validRole = rolesDemandes.Where(p => listRolesDuToken.Contains(p)).Any();

                //validRole = true;
                //validRole = new Users().GetUsers().Where(p => roles.Contains(p.Role) && p.UserName == userName).Any();
            }

            if (validRole)
            {
                context.Succeed(requirement);
            }
            else
            {
                Logger.Error($"L'utilisateur n'a pas le bon role");
                Logger.Trace($"Roles dans le token {string.Join(',', listRolesDuToken)} ");
                Logger.Trace($"Roles attendus {string.Join(',', rolesDemandes)} ");

                context.Fail();
            }
            return Task.CompletedTask;
        }
    }
}
