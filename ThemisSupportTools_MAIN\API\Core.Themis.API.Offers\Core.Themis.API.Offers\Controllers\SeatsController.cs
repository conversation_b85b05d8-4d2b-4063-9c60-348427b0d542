﻿using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

using WebUserWTO = Core.Themis.Libraries.DTO.WTObjects.WebUser;


namespace Core.Themis.API.Offers.Controllers
{
    [ApiController]
    public class SeatsController : ControllerBase
    {
        private readonly IConfiguration _configuration;

        private readonly IMemoryCache _memoryCache;
        private readonly IGestionTraceManager _gestionTrace;
        private readonly IBasketManager _basketManager;
        private readonly IWebUserManager _webUserManager;
        private readonly ISeatManager _seatManager;
        private readonly IEventManager _eventManager;
        private readonly IGestionPlaceManager _gestionPlaceManager;
        private readonly IBuyerProfilManager _buyerProfilManager;

        private static readonly RodrigueNLogger Logger = new();

        public SeatsController(
            IConfiguration configuration, 
            IBasketManager basketManager, 
            IWebUserManager webUserManager,
            IMemoryCache memoryCache, 
            IGestionTraceManager gestionTrace, 
            ISeatManager seatManager,
            IEventManager eventManager,
            IGestionPlaceManager gestionPlaceManager,
            IBuyerProfilManager buyerProfilManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _gestionTrace = gestionTrace;
            _basketManager = basketManager;
            _webUserManager = webUserManager;
            _seatManager = seatManager;
            _eventManager = eventManager;
            _gestionPlaceManager = gestionPlaceManager;
            _buyerProfilManager = buyerProfilManager;
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(FlagResponse))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpPost]
        //[Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/SeatsFlag/auto")]

        public IActionResult FlagAuto(int structureId, [FromBody] FlagDemand flagDemand)
        {
            Logger.Debug(structureId, $"FlagAuto({structureId}) pour webuser {flagDemand.WebUserId}, listDemands.Count = {flagDemand.ListDemands.Count} :{JsonConvert.SerializeObject(flagDemand)}");

            try
            {
                GestionPlaceDTO gp = new();

                FlagResponse flagResponse = new()
                {
                    WebUserId = flagDemand.WebUserId,
                    ListDemands = new List<SessionCategDemand>()
                };

                WebUserWTO webUser = _webUserManager.Get(structureId, flagDemand.WebUserId);

                foreach (SessionCategDemand flg in flagDemand.ListDemands)
                {
                    List<Seat> listSeatsFlages = new();

                    SessionCategDemand ruleResponse = new()
                    {
                        SessionId = flg.SessionId,
                        ZoneId = flg.ZoneId,
                        FloorId = flg.FloorId,
                        SectionId = flg.SectionId,
                        CategoryId = flg.CategoryId,
                        ListRules = new List<rule>()
                    };

                    List<EventDTO> listEvent = new();
                    string cacheName = $"EventManager.GetEventsSessionsCategsPrices.{structureId}.{webUser.IdentiteId}.0.{flg.SessionId}.{webUser.ProfilAcheteurId}";
                    if (!_memoryCache.TryGetValue(cacheName, out listEvent))// Look for cache key.
                    {
                        listEvent = _eventManager.GetEventsSessionsCategsPrices("", structureId, webUser.IdentiteId, 0, flg.SessionId, webUser.ProfilAcheteurId, "");

                        if (listEvent != null)
                        {
                            var cacheEntryOptions = new MemoryCacheEntryOptions()
                            {
                                Priority = CacheItemPriority.High,
                                AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(60)
                            };

                            _memoryCache.Set(cacheName, listEvent, cacheEntryOptions);
                        }
                    }
         
                    if (listEvent == null || listEvent.Count == 0)
                        return Problem($"can't retrieve session {flg.SessionId}", null, StatusCodes.Status404NotFound);

                    var gestionPlaceIdList = listEvent[0].ListSessions[0].ListZones
                                                .SelectMany(z => z.ListFloors
                                                    .SelectMany(f => f.ListSections
                                                        .SelectMany(s => s.ListCategories)
                                                            .Where(cat => cat.CategId == flg.CategoryId)
                                                        .SelectMany(c => c.ListGestionPlace)))
                                                .Select(gp => gp.GestionPlaceId)
                                                .Distinct()
                                                .ToList();

                    int coeff = flg.ListRules.Sum(r => r.SeatsCount);

                    List<GestionPlaceDTO> listGps = _gestionPlaceManager.Load(structureId, gestionPlaceIdList);

                    if (listGps.Count == 0)
                        return Problem($"can't retrieve rules", null, StatusCodes.Status404NotFound);

                    int thisCateg = listGps[0].CategoryId;
                    int thisSession = flg.SessionId;

                    if(listGps.Any(g => g.CategoryId != thisCateg))
                        return Problem($"can't have multi categories in the same demand", null, StatusCodes.Status400BadRequest);

                    Dictionary<int, int> listReservesandCount = new();

                    foreach (GestionPlaceDTO gpe in listGps)
                    {
                        foreach (ReserveDTO res in gpe.listReserves)
                        {
                            if (!listReservesandCount.Keys.Contains(res.ReserveId))
                                listReservesandCount.Add(res.ReserveId, 1);
                            else
                                listReservesandCount[res.ReserveId] += 1;
                        }
                    }

                    var listCommonReserves = listReservesandCount.Where(e => e.Value == listGps.Count).Select(e => e.Key).ToList();
                    if (listCommonReserves.Count == 0)
                    {
                        Logger.Error(structureId, "there is no common reserve with theses rules !");
                        return Problem($"there is no common reserve with theses rules !", null, StatusCodes.Status401Unauthorized);
                    }
                    else
                    {
                        List<List<SeatDTO>> listSeats = _seatManager.FlagAuto(structureId, coeff, flg.SessionId, flg.ZoneId, flg.FloorId, flg.SectionId, thisCateg, listCommonReserves, "T" + flagDemand.WebUserId);

                        ruleResponse.SpitGroupsCount = listSeats.Count;

                        foreach (List<SeatDTO> gr in listSeats)
                        {
                            foreach (SeatDTO s in gr)
                            {
                                listSeatsFlages.Add((Seat)s);
                            }
                        }
                    }

                    int seatAttribueCount = 0;

                    foreach (rule r in flg.ListRules)
                    {
                        rule ruleRep = new()
                        {
                            RuleId = r.RuleId,
                            SeatsCount = listSeatsFlages.Count
                        };

                        if (listSeatsFlages.Count > 0)
                        {
                            ruleRep.ListSeats = new List<Seat>();

                            for (int i = seatAttribueCount; i < seatAttribueCount + r.SeatsCount; i++)
                            {
                                if (listSeatsFlages.Count >= i && listSeatsFlages[i] != null)
                                {
                                    listSeatsFlages[i].SessionId = thisSession;
                                    listSeatsFlages[i].CategoryId = thisCateg;
                                    ruleRep.ListSeats.Add(listSeatsFlages[i]);
                                }
                            }
                            seatAttribueCount += r.SeatsCount;
                        }
                        else
                        {
                            ruleRep.ErrorText = "cannot find free seats";
                        }

                        ruleResponse.ListRules.Add(ruleRep);
                    }


                    flagResponse.ListDemands.Add(ruleResponse);

                }
                _gestionTrace.WriteLogGeneriqueMessage(structureId, flagDemand.WebUserId, "flag ok");

                return Ok(flagResponse);
            }
            catch (Exception ex)
            {

                Logger.Error(structureId, $"FlagAuto({structureId}) pour webuser {flagDemand.WebUserId}, " +
                    $"listDemands.Count = {flagDemand.ListDemands.Count} :{JsonConvert.SerializeObject(flagDemand)} {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<List<SeatDTO>>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpPost]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/SeatsFlag/auto/{eventId}/{sessionId}/{zoneId}/{floorId}/{sectionId}/{webUserId}/{categoryId}/{nseats}")]

        public IActionResult FlagAuto(int structureId, int eventId, int sessionId, int zoneId, int floorId, int sectionId, int webUserId, int categoryId, int nseats, [FromBody] int[] rsvIds)
        {
            try
            {
                Logger.Debug(structureId, $"FlagAuto({structureId}, {eventId}, {sessionId}, {zoneId}, {floorId}, {sectionId}, {webUserId}, {categoryId}, {nseats} seats, rsvs={JsonConvert.SerializeObject(rsvIds)})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                List<List<SeatDTO>> listReturn = _seatManager.FlagAuto(structureId, nseats, sessionId, zoneId, floorId, sectionId, categoryId, rsvIds.ToList(), "T" + webUserId);

                if (listReturn == null || listReturn.Count == 0)
                    Logger.Error(structureId, $"Flag({structureId},{eventId},{sessionId},{categoryId},{nseats},{string.Join(";", rsvIds)}) listReturn est null ou vide ?!?");

                return Ok(listReturn);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"Flag({structureId},{eventId},{sessionId},{categoryId},{nseats},{string.Join(";", rsvIds)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(bool))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpDelete]
        //[Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/SeatsFlag")]

        public IActionResult UnFlag(int structureId, [FromBody] UnFlagDemand unflagDemand)
        {
            try
            {
                Logger.Debug(structureId, $"UnFlag({structureId})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                bool result = true;
                foreach (var session in unflagDemand.ListSessions)
                {
                    List<int> ltounflag = new List<int>();
                    foreach (var s in session.ListSeats)
                    {
                        ltounflag.Add(s.SeatId);
                    }

                    _seatManager.UnFlag(structureId, 0, session.SessionId, unflagDemand.WebUserId, ltounflag, "");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _gestionTrace.WriteLogErrorMessage(structureId, unflagDemand.WebUserId, $"unflag {ex.Message} {ex.StackTrace}");
                Logger.Error(structureId, ex, $"UnFlag({structureId} {ex.Message}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }





        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(bool))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpPost]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/UnFlag/{eventId}/{sessionId}/{webUserId}")]

        public IActionResult UnFlag(int structureId, int eventId, int sessionId, int webUserId, [FromBody] List<int> seatsIds)
        {
            try
            {
                Logger.Debug(structureId, $"UnFlag({structureId},{eventId},{webUserId})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                _seatManager.UnFlag(structureId, 0, sessionId, webUserId, seatsIds, "");

                return Ok(true);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"UnFlag({structureId},{eventId},{sessionId},{string.Join(";", seatsIds)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(bool))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpPost]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/FlagManually/{eventId}/{sessionId}/{webUserId}")]

        public IActionResult FlagManually(int structureId, int eventId, int sessionId, int webUserId, [FromBody] int[] seatsIds)
        {
            try
            {
                Logger.Debug(structureId, $"FlagManually({structureId},{eventId},{webUserId})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);


                var result = _seatManager.FlagManually(structureId, eventId, sessionId, webUserId, seatsIds.ToList(), "T" + webUserId);

                return Ok(result);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"FlagManually({structureId},{eventId},{sessionId},{string.Join(";", seatsIds)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// Ajout de places au panier du user
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="webUserId"></param>
        /// <param name="langCode"></param>
        /// <param name="buyerProfilId"></param>
        /// <param name="bpLogin"></param>
        /// <param name="bpPassword"></param>
        /// <param name="flagDemand"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("api/{structureId}/AddBasket/{langCode}/{buyerProfilId}")]
        [Route("api/{structureId}/AddBasket/{langCode}/{bpLogin}/{bpPassword}")]
        public IActionResult AddSeats(int structureId, string langCode, int buyerProfilId, string bpLogin, string bpPassword, [FromBody] FlagDemand flagDemand)
        {
            Logger.Debug(structureId, $"AddSeats({structureId})...");
            try
            {
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                Logger.Trace(structureId, $"AddBasket({structureId}), {JsonConvert.SerializeObject(flagDemand)}");


                BasketDTO bask = _basketManager.CreateBasketIfNotExists(structureId, flagDemand.WebUserId, 0, "C", null);
                bask.Hash = bask.GetHash();

                int myBprofilId = 0;
                BuyerProfilDTO buyerProfil = _buyerProfilManager.GetBuyerProfilByIdORLoginPassword(structureId, buyerProfilId, bpLogin, bpPassword);
                if (buyerProfil != null)
                {
                    myBprofilId = buyerProfil.Id;
                }

                foreach (SessionCategDemand flg in flagDemand.ListDemands)
                {
                    SessionCategDemand ruleResponse = new SessionCategDemand()
                    {
                        SessionId = flg.SessionId,
                        ZoneId = flg.ZoneId,
                        FloorId = flg.FloorId,
                        SectionId = flg.SectionId,
                        CategoryId = flg.CategoryId,
                        ListRules = new List<rule>()
                    };
                    //

                    WebUserWTO webUser = _webUserManager.Get(structureId, flagDemand.WebUserId);


                    List<EventDTO> listEvent = new();
                    string cacheName = $"EventManager.GetEventsSessionsCategsPrices.{structureId}.{webUser.IdentiteId}.0.{flg.SessionId}.{myBprofilId}";
                    if (!_memoryCache.TryGetValue(cacheName, out listEvent))// Look for cache key.
                    {
                        listEvent = _eventManager.GetEventsSessionsCategsPrices("", structureId, webUser.IdentiteId, 0, flg.SessionId, myBprofilId, "");
                        if (listEvent != null)
                        {
                            var cacheEntryOptions = new MemoryCacheEntryOptions()
                                         //Priority on removing when reaching size limit (memory pressure)
                                         .SetPriority(CacheItemPriority.High)
                                        // Keep in cache for this time, reset time if accessed.                           
                                        // Remove from cache after this time, regardless of sliding expiration
                                        .SetAbsoluteExpiration(TimeSpan.FromSeconds(60));
                            _memoryCache.Set(cacheName, listEvent, cacheEntryOptions);
                        }
                    }


                    if (listEvent == null || listEvent.Count == 0)
                    {
                        Logger.Error(structureId, $"AddBasket({structureId}), can't retrieve session {flg.SessionId}");
                        return Problem($"can't retrieve session {flg.SessionId}", null, StatusCodes.Status404NotFound);
                    }

                    var zones = listEvent[0].ListSessions[0].ListZones;
                    var floors = zones.SelectMany(f => f.ListFloors).ToList();
                    var sections = floors.Distinct().ToList().SelectMany(s => s.ListSections).Distinct().ToList();
                    var categories = sections.Distinct().ToList().SelectMany(c => c.ListCategories).Distinct().ToList();
                    var gpList = categories.Distinct().ToList().SelectMany(gp => gp.ListGestionPlace).Distinct().ToList();


                    List<int> listGpsId = new List<int>();
                    int coeff = 0;
                    foreach (rule r in flg.ListRules)
                    {

                        List<int> listTheseSeatsId = r.ListSeats.Select(s => s.SeatId).ToList();
                        List<SeatDTO> listThesesSeats = _seatManager.Get(structureId, listEvent[0].EventId, listTheseSeatsId, langCode);

                        if (r.RuleId != 0)
                        {
                            foreach (var seat in r.ListSeats)
                            {
                                SeatDTO thisSeat = listThesesSeats.Where(s => s.SeatId == seat.SeatId).FirstOrDefault();

                                var listThisGp = gpList.Where(gp => gp.GestionPlaceId == r.RuleId);
                                if (listThisGp == null || listThisGp.Count() == 0)
                                {
                                    // ne retrouve pas dans la grille de tarif relue le gpId demandé en parametre !!?
                                    // ex: l'internaute s'est logé et n'a plus droit au tarif qu'il a choisi

                                    Logger.Error(structureId, $"AddBasket({structureId}) pour webuser {flagDemand.WebUserId}, can't retrieve rules {r.RuleId}");

                                    return Problem($"can't retrieve rules {r.RuleId}", null, StatusCodes.Status404NotFound);
                                }
                                var thisGp = listThisGp.FirstOrDefault();

                                _basketManager.AddSeat(structureId, bask.BasketId, listEvent[0], listEvent[0].ListSessions[0], thisSeat, thisGp, 0) ;

                            }
                            coeff += r.SeatsCount;
                            listGpsId.Add(r.RuleId);
                        }
                    }
                }



                BasketDTO baskToRet = _basketManager.GetBasketInfo(structureId, bask.BasketId);

                _basketManager.FillFromOpen(structureId, baskToRet, langCode);                

                return Ok(baskToRet);
            }
            catch (Exception ex)
            {

                Logger.Error(structureId, $"AddBasket({structureId}) pour webuser {flagDemand.WebUserId}, " +                
                    $"listDemands.Count = {flagDemand.ListDemands.Count} :{JsonConvert.SerializeObject(flagDemand)} {ex.Message} {ex.StackTrace}"); 

                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
    }
}
