{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true", "ThemisSupportTools": "Server=*************;Database=WSAdmin_dev;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true", "WebTracing": "Data Source=*************;Initial Catalog=WebTracing_dev;User ID=sa;Password=sgYbrkuOw/J1NUUdtFm3nA== MultipleActiveResultSets=true"}, "Path": {"InclusionFilePath": "D:\\WORK\\Features\\CreateCmd_main\\Applis\\ServiceRattrapPanier\\inclusionsStructures.xml", "ExclusionFilePath": "D:\\WORK\\Features\\CreateCmd_main\\Applis\\ServiceRattrapPanier\\exclusionsStructures.xml"}, "ApplicationSettings": {"isDebugMode": "1", "delaySinceSecondes": 600000, "delayToSecondes": 120}, "typeRun": "TEST", "PathForSqlScript": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql", "ConfigIniPath": "\\\\*************\\customerfiles\\TEST\\{structureId}\\CONFIGSERVER\\config.ini.xml"}