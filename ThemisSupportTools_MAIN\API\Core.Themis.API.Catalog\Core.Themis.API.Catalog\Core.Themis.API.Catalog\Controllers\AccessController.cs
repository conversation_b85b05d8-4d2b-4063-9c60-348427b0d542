﻿using Core.Themis.API.Catalog.Helpers.Interfaces;
using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.Managers.Access;
using Core.Themis.Libraries.BLL.Managers.Access.Interfaces;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace Core.Themis.API.Catalog.Controllers
{
    /// <summary>
    /// points d'acces sur le plan de salle
    /// </summary>
    [ApiController]
    public class AccessController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IApiUtilities _apiUtilities;
        private readonly IAccessManager _accessManager;

        private static readonly RodrigueNLogger Logger = new();
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name="memoryCache"></param>
        /// <param name="apiUtilities"></param>
        public AccessController(
            IConfiguration configuration, 
            IMemoryCache memoryCache, 
            IApiUtilities apiUtilities,
            IAccessManager accessManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _apiUtilities = apiUtilities;
            _accessManager = accessManager;
        }

        /// <summary>
        /// list of all the access 
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="codeLang"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<AccessDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        [HttpGet]

        [Route("api/{structureId}/AccessList/{codeLang}")]
       
        public IActionResult AlotissementsList(int structureId, string codeLang)
        {
            try
            {
                Logger.Debug(structureId, $"AccessList({structureId},{codeLang}");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                List<AccessDTO> listS = _accessManager.GetAll(structureId, codeLang);

                Logger.Debug(structureId, $"AccessList({structureId},{codeLang}) ok {listS.Count} access to return");

                return Ok(listS);

            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"AccessList({structureId},{codeLang}) error");
                var pb = Problem(ex.Message, null, StatusCodes.Status500InternalServerError);

                return pb;
            }
        }
    }
}
