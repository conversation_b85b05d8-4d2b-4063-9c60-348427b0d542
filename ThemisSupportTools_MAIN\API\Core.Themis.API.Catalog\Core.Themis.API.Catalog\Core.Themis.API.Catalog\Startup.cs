using Core.Themis.API.Catalog.Handlers;
using Core.Themis.API.Catalog.Helpers;
using Core.Themis.API.Catalog.Helpers.Interfaces;
using Core.Themis.Libraries.BLL.Extentions.ServicesBuilder;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System;
using System.IO;
using System.Text;

namespace Core.Themis.API.Catalog
{
    public class Startup
    {
        private static readonly RodrigueNLogger logger = new RodrigueNLogger();

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            try
            {
                services.AddRodrigueDataServices();
                services.AddRodrigueManager();
                services.AddRodrigueMapper();

                services.AddMemoryCache();
                services.AddScoped<IAuthorizationHandler, RolesAuthorizationHandler>();
                services.AddSingleton<IApiUtilities, ApiUtilities>();


                services.AddAuthentication(options =>
                {
                    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
                })
                .AddJwtBearer(options =>
                {
                    options.SaveToken = true;
                    options.TokenValidationParameters = new TokenValidationParameters()
                    {
                        ValidIssuer = "rodrigue",
                        ValidAudience = "ThemisAPI",
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(this.Configuration["ApiSecretKey"]))
                    };
                });

                services.AddResponseCaching();
                services.AddControllers();
                services.AddRazorPages();
                services.AddSwaggerGen(c =>
                {
                    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Core.Themis.API.Catalog", Version = "v1" });
                });

                services.InitHelpers();
                services.ConfigureHelpers();
            }
            catch (Exception ex)
            {
                logger.Fatal(0, ex, "ConfigureServices");
            }
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.RoutePrefix = "swagger";
                c.SwaggerEndpoint("v1/swagger.json", "process=" + env.EnvironmentName);
            });

            NLog.LogManager.Configuration.Variables.Add("environment", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"));
            string version = Directory.GetParent(AppContext.BaseDirectory).Name;
            NLog.LogManager.Configuration.Variables.Add("version", version);

            app.UseRouting();

            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
