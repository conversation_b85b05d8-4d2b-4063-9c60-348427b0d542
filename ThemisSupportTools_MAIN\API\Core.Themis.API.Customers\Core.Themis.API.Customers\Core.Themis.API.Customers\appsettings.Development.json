{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "Console": {
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Customers.Controllers": "Trace"
      }
    },
    "File": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "RodrigueFileLogger": {
      "Options": {
        "FolderPath": "D:\\LOGS\\Webservices\\API\\CORE_CUSTOMERS",
        "FilePath": "log_{structureid}_{date}.log"
      },
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error",
        "Core.Themis.API.Customers.Controllers": "Information"
      }
    }
  },
  "AllowedHosts": "*",
  "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1",
  "ConnectionStrings": {
    "WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "GlobalOpinionDB": "Server=*************;Database=GlobalOpinions_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true",
    "WebLibraryDB": "Server=*************;Database=GlobalWebLibrary_DEV;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true"
  },

  "WidgetCustomerUrl": "https://dev2.themisweb.fr/widgets/customers/v1/",
  "PartnerRodSK": "ser5#E6V6Z#Mp-7",
  //charge les traductions filtrées par areas
  "TranslationsAreas": {
    "PassBook": [ "PassBook" ]
  },
  "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\TEST\\[structureId]\\CONFIGSERVER\\config.ini.xml",
  "TypeRun": "TEST",
  "CryptoKey": "RodWebShop95",

  // "PathScriptSqlCommons": "\\\\**************\\webservices\\dev\\libraries\\LIBRARIES_SQLSCRIPTS\\[directory\\][filename][.structureid].sql",
  "PathForSqlScriptloc": "..\\..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  "PathForSqlScript": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",

  "WsAdminConnectionCache": 300,
  "CertificatPath": "D:\\API\\DEV\\CUSTOMERS\\1.0.0\\Certificats",
  "CertificatPassword": "Npsfq@99!@",
  "PassbookIconPath": "D:\\API\\DEV\\CUSTOMERS\\1.0.0\\",
  "PathImagesLogos": "\\\\*************\\customerfiles\\TEST\\[structureId]\\paiement\\images\\logosMaquettes\\",
  "PathPdfForViewMaquette": "\\\\**************\\sites\\TEST\\ReprintPDF\\",
  "PathPdf": "\\\\*************\\emails\\TEST\\[structureId]\\pdf\\",
  "PathEmails": "\\\\*************\\emails\\TEST\\[structureId]\\envoisok\\",

  "MTicket": {
    "Google": {
      "AccountFile": "D:\\CERTIFICATS\\GoogleWallet\\Dev\\wallet-369412-463ff125805b.json",
      "IssuerId": 3388000000022257621
    },
    "Apple": {
      "AccountFile": "D:\\CERTIFICATS\\AppleWallet\\Dev\\AppleCredentialParam.json",
      "IconPath": "D:\\API\\DEV\\CUSTOMERS\\1.0.0\\Icons"
    },
    "Samsung": {
      "AccountFile": "D:\\CERTIFICATS\\SamsungWallet\\Local\\SamsungCredentialParam.json",
      "PartnerId": *******************,
      "CardId": "3gl7tatkio500"
    }
  }
}
  