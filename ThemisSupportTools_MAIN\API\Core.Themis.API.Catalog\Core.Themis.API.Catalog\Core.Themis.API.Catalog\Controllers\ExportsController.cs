﻿using Core.Themis.API.Catalog.Helpers.Interfaces;
using Core.Themis.Libraries.BLL.Partners.Interfaces;
using Core.Themis.Libraries.DTO.ExportData.SofwareAG;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace Core.Themis.API.Catalog.Controllers
{

    [ApiController]
    public class ExportsController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IApiUtilities _apiUtilities;
        private readonly ISoftwareAGManager _softwareAGManager;


        private static readonly RodrigueNLogger Logger = new RodrigueNLogger();
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name="wsAdminSqlConnection"></param>
        /// <param name="openRod"></param>
        /// <param name="memoryCache"></param>
        /// <param name="apiUtilities"></param>
        public ExportsController(
            IConfiguration configuration, 
            IMemoryCache memoryCache, 
            IApiUtilities apiUtilities, 
            ISoftwareAGManager softwareAGManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _apiUtilities = apiUtilities;
            _softwareAGManager = softwareAGManager;
        }

        [HttpPost]
        [Route("api/{structureId}/ExportSoftwareAG")]
        public IActionResult ExportSoftwareAG(int structureId)
        {
            try
            {
                Logger.Debug(structureId, $"ExportSoftwareAG({structureId}...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                List<PostSoftwareAGCreateUnidySubsc> listToSend = _softwareAGManager.GetDatasFromDBV2(structureId);

                Logger.Debug(structureId, $"ExportSoftwareAG({structureId} {listToSend.Count} orders to send...");

                if (listToSend != null && listToSend.Count > 0)
                {
                    JObject o2 = new JObject();
                    string configFileTransfer = _configuration["configFileTransfers"];
                    if (System.IO.File.Exists(configFileTransfer))
                    {
                        using (StreamReader file = new StreamReader(configFileTransfer))
                        using (JsonTextReader reader = new JsonTextReader(file))
                        {
                            o2 = (JObject)JToken.ReadFrom(reader);
                        }
                    }

                    JToken myPart = o2.SelectToken("partners[?(@Name=='SOFTWAREAGV2')]");
                    Newtonsoft.Json.Linq.JArray arTransfers = (JArray)myPart["transfers"];

        
                    string type = arTransfers[0]["typeT"].ToString();

                    string endPoint = arTransfers[0]["EndPoint"].ToString();
                    string clientId = arTransfers[0]["clientId"].ToString();
                    string clientSecret = arTransfers[0]["clientSecret"].ToString();
                    string urlOAuth = arTransfers[0]["urlOAuthGetToken"].ToString();

                    string oauth2token = _softwareAGManager.GetTokenOAuth2(urlOAuth, clientId, clientSecret);

                    Logger.Debug(structureId, $"ExportSoftwareAG {structureId}, token = {oauth2token}");

                    string categDayTicket = arTransfers[0]["categDayTicket"].ToString();
                    string categSeasonTicket = arTransfers[0]["categSeasonTicket"].ToString();

                    foreach (var post in listToSend)
                    {
                        foreach (var s in post.listSubsc)
                        {
                            if (string.IsNullOrEmpty(s.formula_name))
                                s.subscription_category_id = categDayTicket;
                            else
                                s.subscription_category_id = categSeasonTicket;
                        }
                    }

                    int i = 0;

                    int iThisToken = 0;

                    foreach (var post in listToSend)
                    {

                        if (iThisToken > 100) // recharger un token tous les 100 subscriptions
                        {
                            oauth2token = _softwareAGManager.GetTokenOAuth2(urlOAuth, clientId, clientSecret);
                            Logger.Debug(structureId, $"ExportSoftwareAG {structureId}, new token = {oauth2token}");
                            iThisToken = 0;
                        }
                        iThisToken++;

                        i++;
                        Logger.Debug(structureId, $"ExportSoftwareAG({structureId} {i}/{listToSend.Count}, order #{post.OrderId}, (contains {post.listSubsc.Count} subs)...");
                        Task<bool> taskPost = _softwareAGManager.PostDataV2(structureId, endPoint, oauth2token,  post, 0) ;
                        taskPost.Wait();
                        bool ok = taskPost.Result;

                        if (ok)
                        {
                            bool updateOk = _softwareAGManager.UpdateSubscrSended(structureId, post);
                        }
                        else
                        {
                            Logger.Error(structureId, $"ExportSoftwareAG({structureId} {i}/{listToSend.Count}, order #{post.OrderId}, (contains {post.listSubsc.Count} subs) not ok!");
                        }
                    }
                }
                return Ok();
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"ExportSoftwareAG({structureId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
    }
}
