﻿using Microsoft.AspNetCore.Http;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Core.Themis.API.Offers
{
    public static class PartnerRightsChecker
    {
        /// <summary>
        /// verifier si le token contient la structure 
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public static bool PartnerHasRight(int structureId, Microsoft.Extensions.Primitives.StringValues accT)
        {
            if (AuthenticationHeaderValue.TryParse(accT, out var headerValue))
            {

                var parameter = headerValue.Parameter;

                if (parameter == null)
                    return false;

                var tokenHandler = new JwtSecurityTokenHandler();
                var jsonToken = tokenHandler.ReadJwtToken(parameter);
                //jsonToken.Claims[]
                //var toto = jsonToken.Claims.Any(claim => claim.Type == ClaimTypes.UserData);
                var listClaims = jsonToken.Claims.Where(claim => claim.Type == ClaimTypes.UserData).ToList();
                if (listClaims.Where(cl => cl.Value == "*").Any())
                {
                    return true; // claims contient "*" => toutes les structures demandées sont acceptées
                }
                else
                {

                    bool strucInClaims = listClaims.Where(cl => cl.Value == structureId.ToString()).Any();
                    // claims contient la structure demandée => peut passer
                    return strucInClaims;
                }

            }


            return false;
        }
    }
}
