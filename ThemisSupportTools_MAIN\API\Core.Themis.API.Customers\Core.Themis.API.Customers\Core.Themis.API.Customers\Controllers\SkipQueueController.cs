﻿
using Core.Themis.Libraries.BLL.Managers.Queue.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;

namespace Core.Themis.API.Customers.Controllers
{
    //[Route("api/[controller]")]
    [ApiController]
    public class SkipQueueController : ControllerBase
    {

        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
  
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;
        private readonly ISkipQueueManager _skipQueueManager;

        private static readonly RodrigueNLogger Logger = new();


        public SkipQueueController(
            IConfiguration configuration, 
            IMemoryCache memoryCache, 
 
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary,
            ISkipQueueManager skipQueueManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
   
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
            _skipQueueManager = skipQueueManager;
        }

        /// <summary>
        /// Charge les commandes de l'internaute qui a acheté chez le revendeur
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="skipQueue"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<SkipQueueDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        [HttpGet]
        [Route("api/{structureId}/skipqueue/orders/{langCode}")]
        public IActionResult LoadOrdersOfInternetUser(int structureId, string langCode, [FromQuery] SkipQueueDTO skipQueue)
        {
            try
            {
                Logger.Debug(structureId, $"LoadOrdersOfInternetUser({structureId}");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                var eventsList = _skipQueueManager.LoadOrdersOfInternetUser(structureId, skipQueue);
                Logger.Debug(structureId, $"LoadOrdersOfInternetUser({structureId}) ok {eventsList.Count} events to return");

                return Ok(eventsList);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"LoadOrdersOfInternetUser({structureId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

    }
}
