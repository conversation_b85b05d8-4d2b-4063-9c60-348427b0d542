﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

using System.Web.Http.Description;

namespace Core.Themis.Api.External
{

    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Class)]
    public class PublicApi : Attribute
    {

    }

    public class ExcludeVersionFromSwagger : Swashbuckle.AspNetCore.SwaggerGen.IDocumentFilter
    {
        public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
        {
            var excludedControllers = new[] { "PassCulture" };  // Exclure ce contrôleur
            foreach (var path in swaggerDoc.Paths.ToList())
            {
                if (excludedControllers.Any(x => path.Key.Contains(x)))
                {
                    swaggerDoc.Paths.Remove(path.Key);
                }
            }
        }
    }


}

