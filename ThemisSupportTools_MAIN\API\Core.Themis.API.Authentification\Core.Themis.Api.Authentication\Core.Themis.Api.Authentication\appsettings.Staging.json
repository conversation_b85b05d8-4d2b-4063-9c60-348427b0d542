{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "TypeRun": "TEST", "AudienceToken": "ThemisAPI", "PathForSqlScript": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql", "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1", "ConnectionStrings": {"WsAdminDBPROD": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true", "WsAdminDB": "Server=*************;Database=WSAdmin_test;Persist Security Info=True;User ID=SupportTest;Password=****************************************************************************************;MultipleActiveResultSets=true", "Redis": "**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl"}}