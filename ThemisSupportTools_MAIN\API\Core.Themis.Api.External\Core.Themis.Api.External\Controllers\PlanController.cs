﻿using AutoMapper;
using Core.Themis.Api.External.Helpers.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Net.Http.Headers;

namespace Core.Themis.Api.External.Controllers
{
    /// <summary>
    /// Work a seats map
    /// </summary>  

    [ApiController]
    [ApiExplorerSettings(GroupName = "ext")]
    public class PlanController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IApiOffersClient _apiOffersClients;
        private readonly ISessionManager _sessionManager;
        private readonly IBuyerProfilManager _bpManager;
        private readonly IPlaceObjectManager _placeObjectManager;
        private readonly IMapper _mapper;

        private static readonly RodrigueNLogger Logger = new RodrigueNLogger();


        public PlanController(IConfiguration configuration, IMemoryCache memoryCache, IMapper mapper, IApiOffersClient apiOffersClient,
            ISessionManager sessionManager, IBuyerProfilManager bpManager, IPlaceObjectManager placeObjectManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _mapper = mapper;
            _sessionManager = sessionManager;
            _bpManager = bpManager;
            _placeObjectManager = placeObjectManager;
        }

        /// <summary>
        /// Get all seats of a session
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="sessionId">The session identifier</param>
        /// <returns></returns>
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Seat>))]
        [HttpGet]
        [ResponseCache(Duration = 60)]
        [Route("api/{structureId}/Plan/{sessionId}")]
        public IActionResult Plan(int structureId, int sessionId)
        { 
            Logger.Debug(structureId, $"Plan({structureId},{sessionId})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed !", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            string codeLangTODO = "fr";

            try
            {
                List<SeatDTO> seatsList = _sessionManager.LoadSeatPlan(structureId, 0, 0, sessionId, 0, 0, 0, 0, "", codeLangTODO, 0);
                List<Seat> listS = _mapper.Map<List<Seat>>(seatsList);

                if (listS != null)
                {
                    return Ok(listS);
                }
                else
                    return Problem("return null", null, StatusCodes.Status500InternalServerError);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Plan({structureId}) , " +
                    $"{ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }



        /// <summary>
        /// Get seats
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="sessionId">The session identifier</param>
        /// <param name="identityId">The customer identifier</param>
        /// <param name="webUserId">The web user identifier</param>
        /// <param name="bpLogin">The buyer profil login</param>
        /// <param name="bpPassword">The buer profil password</param>
        /// <returns></returns>
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Seat>))]
        [HttpGet]
        [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        [Route("api/{structureId}/Plan/{sessionId}/availables/{identityId}/{webUserId}/{bpLogin}/{bpPassword}")]
        public IActionResult PlanAvailables(int structureId, int sessionId, int identityId, int webUserId, string bpLogin, string bpPassword)
        {
            //return Ok();


            Logger.Debug(structureId, $"Plan.availables({structureId},{sessionId},{bpLogin},{bpPassword})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            string codeLangTODO = "fr";

            try
            {
                int thisBPId = 0;
                BuyerProfilDTO? bp = _bpManager.GetBuyerProfilByLoginPassword(structureId, bpLogin, bpPassword);
                if (bp != null)
                {
                    thisBPId = bp.Id;
                }

                List<SeatDTO> seatsList = _sessionManager.LoadSeatPlanAvailables(structureId, 0, 0, sessionId, 0, 0, 0, identityId, codeLangTODO, thisBPId, webUserId) ;               
                List<Seat> listS = _mapper.Map<List<Seat>>(seatsList);

                if (listS != null)
                {
                    return Ok(listS);
                }
                else
                    return Problem("return null", null, StatusCodes.Status500InternalServerError);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Plan({structureId}) , " +
                    $"{ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// Get texts of a session
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="sessionId">The session identifier</param>
        /// <returns></returns>
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<TextPlan>))]
        [HttpGet]
        [ResponseCache(Duration = 600)]
        [Route("api/{structureId}/Plan/{sessionId}/texts")]
        public IActionResult PlanTexts(int structureId, int sessionId)
        {
            Logger.Debug(structureId, $"Plan texts ({structureId},{sessionId})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            try
            {
                List<TextOrLineSeatingPlanDTO> seatsList = _sessionManager.LoadTextsPlan(structureId, 0, 0, sessionId, null, null, null);
                List<TextPlan> listS = _mapper.Map<List<TextPlan>>(seatsList);

                if (listS != null)
                {
                    return Ok(listS);
                }
                else
                    return Problem("return null", null, StatusCodes.Status500InternalServerError);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Plan({structureId}) , " +
                    $"{ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// Get categories of session
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="sessionId">The session identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <returns></returns>
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Category>))]
        [HttpGet]
        [ResponseCache(Duration = 60)]
        [Route("api/{structureId}/Plan/{sessionId}/{langCode}/categories")]
        public IActionResult PlanCategories(int structureId, int sessionId, string langCode)
        {
            Logger.Debug(structureId, $"Plan categories ({structureId},{sessionId}, {langCode})...");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            try
            {
                List<CategoryDTO> listCategs = _placeObjectManager.GetCategsBySession(structureId, langCode, sessionId);

                List<Category> listS = _mapper.Map<List<Category>>(listCategs);

                if (listS != null)
                {
                    return Ok(listS);
                }
                else
                    return Problem("return null", null, StatusCodes.Status500InternalServerError);

            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Plan({structureId}) , " +
                    $"{ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
    }
}
