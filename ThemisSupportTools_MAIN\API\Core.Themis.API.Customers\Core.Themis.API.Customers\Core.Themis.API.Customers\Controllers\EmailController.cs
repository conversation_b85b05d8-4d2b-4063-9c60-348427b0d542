﻿using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.AccessControl.Interfaces;
using Core.Themis.Libraries.BLL.Helpers;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Interfaces;
using Core.Themis.Libraries.DTO.ExportData;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.Utilities.CustomErrors;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Net.Mail;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using static Core.Themis.Libraries.Utilities.Helpers.Sql.ExpressionHelper;

namespace Core.Themis.API.Customers.Controllers
{
    /// <summary>
    /// emails stuff
    /// </summary>

    [ApiController]
    public class EmailController : ControllerBase
    {
        private static readonly RodrigueNLogger Logger = new();
        private readonly IConfiguration _configuration;
        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;
        private readonly IPrintHomeManager _printHomeManager;
        private readonly IIdentiteManager _identiteManager;
        private readonly ITranslateManager _translateManager;
        private readonly ISeatManager _seatManager;
        private readonly IEnvoiPdfdepuisOpenRepository _envoiPdfdepuisOpenRepository;
        public EmailController(
            IConfiguration configuration,
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary,
            IPrintHomeManager printHomeManager,
            ITranslateManager translateManager,
            IIdentiteManager identiteManager,
            IEnvoiPdfdepuisOpenRepository envoiPdfdepuisOpenRepository,
            ISeatManager seatManager)
        {
            _configuration = configuration;
            _printHomeManager = printHomeManager;
            _translateManager = translateManager;
            _identiteManager = identiteManager;
            _seatManager = seatManager;
            _envoiPdfdepuisOpenRepository = envoiPdfdepuisOpenRepository;
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
        }

        /// <summary>
        /// Send un email sur une commande déjà editée
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="identityId"></param>
        /// <param name="reqSendpdf"></param>
        /// <param name="orderId"></param>
        /// <param name="langCode"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        //[Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/sendEmailFromRod/{langCode}/{orderId}/{identityId}/{posteId}")]
        public IActionResult SendEmail(int structureId, string langCode, int orderId, int identityId, 
            int posteId, string posteName, string? emailTo,
            [FromBody] BodySendEmailOrder reqSendpdf
            )
        {
            try
            {
                string formDataToJson = JsonConvert.SerializeObject(reqSendpdf);
                Logger.Info(structureId, $"{langCode}/{orderId}/{identityId}/{posteId} => formData:{formDataToJson}");

                if (reqSendpdf.LstEntrees.Count + reqSendpdf.LstProduits.Count == 0)
                {
                    return BadRequest($"request_sendpdf is empty !");
                }

                var identFound = _identiteManager.Get(structureId, identityId.ToString(), langCode, "");

     

                if (identFound == null)
                {
                    identFound = new Libraries.DTO.Identity.IdentityDTO()
                    {
                        IdentiteId = identityId,
                    };
                   // return Problem($"can't find identityId {identityId}", null);
                }
                if (!string.IsNullOrEmpty(emailTo))
                {
                    identFound.Email = emailTo;
                }

                string smptIp = _rodrigueConfigIniDictionnary.GetValue(structureId, "SMTPCLIENTIP");

                var emailSenderAdress = _rodrigueConfigIniDictionnary.GetValue(structureId, "EMAILSENDERADRESSE");


                var emailSenderName = _rodrigueConfigIniDictionnary.GetValue(structureId, "EMAILSENDERNAME");
                if (!String.IsNullOrEmpty(emailSenderName))
                    emailSenderName = HttpUtility.HtmlDecode(emailSenderName);

                var smtpClient = new SmtpClient(smptIp);



                int paramId = (int)_envoiPdfdepuisOpenRepository.Insert(structureId, new Libraries.Data.Entities.Open.Order.EnvoiPdfdepuisOpenEntity()
                {
                    Poste = posteName,
                    PosteId = posteId,
                    CommandeId = orderId,
                    DateOperation = DateTime.Now,
                    Paramsjson = formDataToJson,
                });


                bool result = EmailsHelper.SendEmail_sendOrderToCustomer(structureId, langCode, smptIp, emailSenderAdress, emailSenderName, orderId, identFound, null, paramId, "");



                //[Route("sendEmailFromRodGetView/{structureId}/{langCode}/{recipIdentiteId}")]







                return Ok(result);

            }

            catch (Exception ex)
            {
                return Problem(ex.Message, null);
            }





        }

    }



}
