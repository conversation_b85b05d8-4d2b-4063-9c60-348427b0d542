﻿using Core.Themis.API.Catalog.Helpers.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.Utilities.Crypto;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Configuration;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace Core.Themis.API.Offers.Middleware
{

    public static class SignatureValidatorsExtension
    {
        public static IApplicationBuilder ApplySignatureValidation(this IApplicationBuilder app)
        {
            app.UseMiddleware<SignatureValidatorsMiddleware>();
            return app;
        }
    }


    public class SignatureValidatorsMiddleware
    {

        private readonly RequestDelegate _next;

        public SignatureValidatorsMiddleware(
            RequestDelegate next
           )
        {
            _next = next;
          
        }
        public async Task Invoke(HttpContext context, IPartnerManager partnerManager)
        {
            if (!context.Request.Headers.Keys.Contains("Signature"))
            {
                context.Response.StatusCode = 400; //Bad Request                
                await context.Response.WriteAsync("Signature is missing");
                return;
            }
            else
            {
                //if (!contactsRepo.CheckValidUserKey(context.Request.Headers["user-key"]))
                string mySignatureRecue = context.Request.Headers["Signature"].ToString();

                string displUrl = context.Request.GetDisplayUrl();

                if (context.Request.Path != "/Error")
                {

                    int myId = int.Parse(context.User.Identity.Name);
                    PartnerDTO part = partnerManager.GetPartnerInfosById( myId);

                    string mySecretKey = part.SecretKey;
                    string toHash = context.Request.Method + " " + displUrl;


                    string signatureCalculee = ApiSignatureManager.GeneratePartnerSignature(toHash, mySecretKey);
                    if (signatureCalculee != mySignatureRecue)
                    {

                        //https://localhost:44329/api/CrossSelling?structureId=994

                        context.Response.StatusCode = 401; //UnAuthorized
                        await context.Response.WriteAsync("Signature invalid");
                        return;
                    }
                }

            }
            await _next.Invoke(context);
        }
    }


}
