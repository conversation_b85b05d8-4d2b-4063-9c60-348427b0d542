       --cmdWT.Parameters.AddWithValue("pIdentiteId", identityId);
		--cmdWT.Parameters.AddWithValue("pPriceId", ThisGestionPlaceEntity.PriceId);
		--cmdWT.Parameters.AddWithValue("pSessionId", ThisGestionPlaceEntity.SessionId);
          --                              cmdWT.Parameters.AddWithValue("pEventId", ThisGestionPlaceEntity.EventId);


/* CheckGpIdEnCours */
/* ******* verif que le consumer n'a pas déjà ce tarif, cette manif dans son panier */

declare @n int =0
select @n = count(*) from panier_produit pp
inner join panier_produit_carteadhesion_props prop on prop.panier_produit_id = pp.panier_produit_id
inner join panier p on p.panier_id = pp.panier_id 
where p.etat ='C'
and pp.produit_id = @pProdId
and prop.consumer_id =@pidentiteid

if @n = 0
begin
	select 'ok' as ret
end
else
begin
	select 'ko' as ret
end




