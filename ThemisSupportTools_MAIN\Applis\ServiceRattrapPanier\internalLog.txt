2024-11-07 17:51:21.8262 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-07 17:51:21.8262 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-07 17:51:21.8562 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-environment' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:51:21.8562 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-environment' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:51:21.8562 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:51:21.8562 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:51:21.8660 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-07 17:51:21.8660 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-environment' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:51:21.8660 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-environment' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:51:21.8660 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-07 17:51:21.8660 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-environment' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:51:21.8660 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-environment' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:51:21.8660 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-07 17:51:21.8660 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-07 17:51:21.8660 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-07 17:51:21.8660 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-07 17:51:21.8660 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-07 17:51:21.8811 Info Configuration initialized.
2024-11-07 17:53:44.8964 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-07 17:53:44.9006 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-07 17:53:44.9253 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:53:44.9253 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:53:44.9253 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-07 17:53:44.9253 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-07 17:53:44.9253 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-07 17:53:44.9253 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-07 17:53:44.9253 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-07 17:53:44.9253 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-07 17:53:44.9253 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-07 17:53:44.9444 Info Configuration initialized.
2024-11-07 17:55:37.1206 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-07 17:55:37.1206 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-07 17:55:37.1509 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:55:37.1509 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-07 17:55:37.1509 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-07 17:55:37.1509 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-07 17:55:37.1509 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-07 17:55:37.1601 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-07 17:55:37.1601 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-07 17:55:37.1601 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-07 17:55:37.1601 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-07 17:55:37.1601 Info Configuration initialized.
2024-11-08 10:04:03.6465 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 10:04:03.6526 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 10:04:03.6872 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 10:04:03.6872 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 10:04:03.6922 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 10:04:03.6922 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 10:04:03.6922 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 10:04:03.6922 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 10:04:03.6922 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 10:04:03.6922 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 10:04:03.6922 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 10:04:03.7059 Info Configuration initialized.
2024-11-08 10:26:16.6143 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 10:26:16.6204 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 10:26:16.6567 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 10:26:16.6567 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 10:26:16.6567 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 10:26:16.6567 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 10:26:16.6567 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 10:26:16.6567 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 10:26:16.6567 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 10:26:16.6567 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 10:26:16.6692 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 10:26:16.6692 Info Configuration initialized.
2024-11-08 12:04:54.3622 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 12:04:54.3696 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 12:04:54.4094 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:04:54.4094 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:04:54.4094 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 12:04:54.4094 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 12:04:54.4094 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 12:04:54.4094 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:04:54.4094 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:04:54.4094 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 12:04:54.4232 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 12:04:54.4232 Info Configuration initialized.
2024-11-08 12:06:49.1219 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 12:06:49.1285 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 12:06:49.1651 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:06:49.1651 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:06:49.1699 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 12:06:49.1699 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 12:06:49.1699 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 12:06:49.1699 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:06:49.1699 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:06:49.1699 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 12:06:49.1699 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 12:06:49.1699 Info Configuration initialized.
2024-11-08 12:07:18.4398 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 12:07:18.4470 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 12:07:18.4826 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:07:18.4826 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:07:18.4826 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 12:07:18.4826 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 12:07:18.4826 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 12:07:18.4826 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:07:18.4826 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:07:18.4826 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 12:07:18.4826 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 12:07:18.4999 Info Configuration initialized.
2024-11-08 12:07:35.2692 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 12:07:35.2692 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 12:07:35.3101 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:07:35.3101 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:07:35.3101 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 12:07:35.3101 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 12:07:35.3101 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 12:07:35.3101 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:07:35.3101 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:07:35.3101 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 12:07:35.3101 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 12:07:35.3276 Info Configuration initialized.
2024-11-08 12:10:33.7483 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 12:10:33.7548 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 12:10:33.7909 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:10:33.7909 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:10:33.7957 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 12:10:33.7957 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 12:10:33.7957 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 12:10:33.7957 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:10:33.7957 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:10:33.7957 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 12:10:33.7957 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 12:10:33.7957 Info Configuration initialized.
2024-11-08 12:15:19.4996 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 12:15:19.5055 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 12:15:19.5385 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:15:19.5385 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 12:15:19.5385 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 12:15:19.5439 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 12:15:19.5439 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 12:15:19.5439 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:15:19.5439 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 12:15:19.5439 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 12:15:19.5439 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 12:15:19.5439 Info Configuration initialized.
2024-11-08 15:09:26.0327 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 15:09:26.0394 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 15:09:26.0766 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 15:09:26.0766 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 15:09:26.0766 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 15:09:26.0766 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 15:09:26.0766 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 15:09:26.0766 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 15:09:26.0766 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 15:09:26.0766 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 15:09:26.0766 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 15:09:26.0952 Info Configuration initialized.
2024-11-08 15:10:37.7898 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 15:10:37.7962 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 15:10:37.8352 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 15:10:37.8352 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 15:10:37.8352 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 15:10:37.8352 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 15:10:37.8352 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 15:10:37.8433 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 15:10:37.8433 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 15:10:37.8433 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 15:10:37.8433 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 15:10:37.8433 Info Configuration initialized.
2024-11-08 15:36:37.3551 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 15:36:37.3613 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 15:36:37.3980 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 15:36:37.3980 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 15:36:37.3980 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 15:36:37.3980 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 15:36:37.4042 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 15:36:37.4042 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 15:36:37.4042 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 15:36:37.4042 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 15:36:37.4042 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 15:36:37.4042 Info Configuration initialized.
2024-11-08 15:36:39.8508 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-08 15:36:39.8571 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-08 15:36:39.8923 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 15:36:39.8923 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-08 15:36:39.8923 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-08 15:36:39.8923 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-08 15:36:39.8923 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-08 15:36:39.8923 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 15:36:39.8923 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-08 15:36:39.8923 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-08 15:36:39.9047 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-08 15:36:39.9047 Info Configuration initialized.
2024-11-12 09:42:13.2748 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 09:42:13.2826 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 09:42:13.3207 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 09:42:13.3207 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 09:42:13.3207 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 09:42:13.3207 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 09:42:13.3207 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 09:42:13.3295 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 09:42:13.3295 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 09:42:13.3295 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 09:42:13.3295 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 09:42:13.3295 Info Configuration initialized.
2024-11-12 09:43:58.7919 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 09:43:58.8000 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 09:43:58.8339 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 09:43:58.8339 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 09:43:58.8339 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 09:43:58.8339 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 09:43:58.8339 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 09:43:58.8339 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 09:43:58.8339 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 09:43:58.8428 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 09:43:58.8428 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 09:43:58.8428 Info Configuration initialized.
2024-11-12 09:59:51.1396 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 09:59:51.1396 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 09:59:51.1764 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 09:59:51.1764 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 09:59:51.1808 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 09:59:51.1808 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 09:59:51.1808 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 09:59:51.1808 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 09:59:51.1808 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 09:59:51.1808 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 09:59:51.1808 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 09:59:51.1935 Info Configuration initialized.
2024-11-12 10:01:25.2680 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 10:01:25.2783 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 10:01:25.3131 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 10:01:25.3131 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 10:01:25.3131 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 10:01:25.3189 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 10:01:25.3189 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 10:01:25.3189 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 10:01:25.3189 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 10:01:25.3189 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 10:01:25.3189 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 10:01:25.3189 Info Configuration initialized.
2024-11-12 10:10:12.2506 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 10:10:12.2578 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 10:10:12.2902 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 10:10:12.2902 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 10:10:12.2902 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 10:10:12.2902 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 10:10:12.2902 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 10:10:12.2986 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 10:10:12.2986 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 10:10:12.2986 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 10:10:12.2986 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 10:10:12.2986 Info Configuration initialized.
2024-11-12 11:26:11.2250 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 11:26:11.2311 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 11:26:11.2654 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 11:26:11.2654 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 11:26:11.2654 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 11:26:11.2654 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 11:26:11.2654 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 11:26:11.2654 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 11:26:11.2654 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 11:26:11.2654 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 11:26:11.2790 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 11:26:11.2790 Info Configuration initialized.
2024-11-12 11:28:07.4145 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 11:28:07.4202 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 11:28:07.4514 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 11:28:07.4514 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 11:28:07.4514 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 11:28:07.4514 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 11:28:07.4514 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 11:28:07.4514 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 11:28:07.4514 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 11:28:07.4514 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 11:28:07.4514 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 11:28:07.4689 Info Configuration initialized.
2024-11-12 11:37:04.0846 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 11:37:04.0905 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 11:37:04.1226 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 11:37:04.1226 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 11:37:04.1226 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 11:37:04.1226 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 11:37:04.1226 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 11:37:04.1308 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 11:37:04.1308 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 11:37:04.1308 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 11:37:04.1308 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 11:37:04.1308 Info Configuration initialized.
2024-11-12 12:06:38.2471 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 12:06:38.2534 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 12:06:38.2859 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 12:06:38.2859 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 12:06:38.2859 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 12:06:38.2859 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 12:06:38.2859 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 12:06:38.2859 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 12:06:38.2859 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 12:06:38.2943 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 12:06:38.2943 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 12:06:38.2943 Info Configuration initialized.
2024-11-12 12:24:26.7135 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 12:24:26.7200 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 12:24:26.7522 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 12:24:26.7522 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 12:24:26.7522 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 12:24:26.7574 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 12:24:26.7574 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 12:24:26.7574 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 12:24:26.7574 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 12:24:26.7574 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 12:24:26.7574 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 12:24:26.7574 Info Configuration initialized.
2024-11-12 12:31:10.0501 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 12:31:10.0561 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 12:31:10.0873 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 12:31:10.0873 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 12:31:10.0873 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 12:31:10.0873 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 12:31:10.0873 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 12:31:10.0873 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 12:31:10.0873 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 12:31:10.0873 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 12:31:10.0997 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 12:31:10.0997 Info Configuration initialized.
2024-11-12 13:25:42.8572 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 13:25:42.8572 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 13:25:42.8993 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 13:25:42.8993 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 13:25:42.8993 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 13:25:42.8993 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 13:25:42.8993 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 13:25:42.8993 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 13:25:42.8993 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 13:25:42.8993 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 13:25:42.8993 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 13:25:42.9155 Info Configuration initialized.
2024-11-12 13:36:18.0563 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 13:36:18.0563 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 13:36:18.0944 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 13:36:18.0944 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 13:36:18.0987 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 13:36:18.0987 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 13:36:18.0987 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 13:36:18.0987 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 13:36:18.0987 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 13:36:18.0987 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 13:36:18.0987 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 13:36:18.0987 Info Configuration initialized.
2024-11-12 14:46:52.5297 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 14:46:52.5297 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 14:46:52.5676 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 14:46:52.5676 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 14:46:52.5676 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 14:46:52.5727 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 14:46:52.5727 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 14:46:52.5727 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 14:46:52.5727 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 14:46:52.5727 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 14:46:52.5727 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 14:46:52.5727 Info Configuration initialized.
2024-11-12 16:03:27.1819 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-12 16:03:27.1819 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-12 16:03:27.2200 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 16:03:27.2235 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-12 16:03:27.2235 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-12 16:03:27.2235 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-12 16:03:27.2235 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-12 16:03:27.2235 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 16:03:27.2235 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-12 16:03:27.2235 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-12 16:03:27.2235 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-12 16:03:27.2414 Info Configuration initialized.
2024-11-13 17:42:13.5357 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-13 17:42:13.5357 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-13 17:42:13.5757 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-13 17:42:13.5770 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-13 17:42:13.5770 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-13 17:42:13.5770 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-13 17:42:13.5770 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-13 17:42:13.5770 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-13 17:42:13.5770 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-13 17:42:13.5770 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-13 17:42:13.5770 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-13 17:42:13.5933 Info Configuration initialized.
2024-11-13 17:48:35.4303 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-13 17:48:35.4363 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-13 17:48:35.4691 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-13 17:48:35.4691 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-13 17:48:35.4691 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-13 17:48:35.4691 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-13 17:48:35.4691 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-13 17:48:35.4691 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-13 17:48:35.4691 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-13 17:48:35.4691 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-13 17:48:35.4691 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-13 17:48:35.4874 Info Configuration initialized.
2024-11-14 11:10:42.6385 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 11:10:42.6450 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 11:10:42.6749 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:10:42.6749 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:10:42.6749 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 11:10:42.6749 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 11:10:42.6749 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 11:10:42.6749 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:10:42.6749 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:10:42.6749 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 11:10:42.6749 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 11:10:42.6924 Info Configuration initialized.
2024-11-14 11:13:10.5204 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 11:13:10.5294 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 11:13:10.5594 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:13:10.5594 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:13:10.5594 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 11:13:10.5594 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 11:13:10.5594 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 11:13:10.5669 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:13:10.5669 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:13:10.5669 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 11:13:10.5669 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 11:13:10.5669 Info Configuration initialized.
2024-11-14 11:15:34.0756 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 11:15:34.0818 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 11:15:34.1143 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:15:34.1143 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:15:34.1143 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 11:15:34.1143 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 11:15:34.1143 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 11:15:34.1143 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:15:34.1143 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:15:34.1143 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 11:15:34.1267 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 11:15:34.1267 Info Configuration initialized.
2024-11-14 11:20:18.5929 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 11:20:18.5988 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 11:20:18.6293 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:20:18.6293 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:20:18.6293 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 11:20:18.6293 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 11:20:18.6293 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 11:20:18.6293 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:20:18.6293 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:20:18.6293 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 11:20:18.6410 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 11:20:18.6410 Info Configuration initialized.
2024-11-14 11:22:42.4484 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 11:22:42.4543 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 11:22:42.4844 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:22:42.4844 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:22:42.4844 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 11:22:42.4844 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 11:22:42.4844 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 11:22:42.4844 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:22:42.4844 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:22:42.4844 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 11:22:42.4844 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 11:22:42.5019 Info Configuration initialized.
2024-11-14 11:25:31.1117 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 11:25:31.1117 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 11:25:31.1486 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:25:31.1486 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:25:31.1486 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 11:25:31.1540 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 11:25:31.1540 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 11:25:31.1540 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:25:31.1540 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:25:31.1540 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 11:25:31.1540 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 11:25:31.1540 Info Configuration initialized.
2024-11-14 11:39:52.7263 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 11:39:52.7322 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 11:39:52.7650 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:39:52.7650 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:39:52.7650 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 11:39:52.7650 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 11:39:52.7650 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 11:39:52.7650 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:39:52.7650 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:39:52.7650 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 11:39:52.7770 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 11:39:52.7770 Info Configuration initialized.
2024-11-14 11:51:08.7951 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 11:51:08.7951 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 11:51:08.8315 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:51:08.8315 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:51:08.8363 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 11:51:08.8363 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 11:51:08.8363 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 11:51:08.8363 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:51:08.8363 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:51:08.8363 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 11:51:08.8363 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 11:51:08.8363 Info Configuration initialized.
2024-11-14 11:51:48.8026 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 11:51:48.8085 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 11:51:48.8395 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:51:48.8395 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:51:48.8395 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 11:51:48.8395 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 11:51:48.8395 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 11:51:48.8395 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:51:48.8395 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:51:48.8395 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 11:51:48.8515 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 11:51:48.8515 Info Configuration initialized.
2024-11-14 11:55:40.1316 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 11:55:40.1374 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 11:55:40.1678 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:55:40.1678 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:55:40.1678 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 11:55:40.1678 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 11:55:40.1678 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 11:55:40.1678 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:55:40.1678 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:55:40.1678 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 11:55:40.1794 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 11:55:40.1794 Info Configuration initialized.
2024-11-14 11:56:51.2751 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 11:56:51.2751 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 11:56:51.3129 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:56:51.3129 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 11:56:51.3175 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 11:56:51.3175 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 11:56:51.3175 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 11:56:51.3175 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:56:51.3175 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 11:56:51.3175 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 11:56:51.3175 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 11:56:51.3175 Info Configuration initialized.
2024-11-14 12:01:33.5609 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 12:01:33.5679 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 12:01:33.5997 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:01:33.5997 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:01:33.5997 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 12:01:33.5997 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 12:01:33.5997 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 12:01:33.5997 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:01:33.5997 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:01:33.5997 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 12:01:33.5997 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 12:01:33.6169 Info Configuration initialized.
2024-11-14 12:02:39.1005 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 12:02:39.1069 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 12:02:39.1409 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:02:39.1409 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:02:39.1454 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 12:02:39.1454 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 12:02:39.1454 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 12:02:39.1454 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:02:39.1454 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:02:39.1454 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 12:02:39.1454 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 12:02:39.1454 Info Configuration initialized.
2024-11-14 12:04:40.8072 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 12:04:40.8072 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 12:04:40.8469 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:04:40.8469 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:04:40.8469 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 12:04:40.8469 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 12:04:40.8469 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 12:04:40.8469 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:04:40.8469 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:04:40.8469 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 12:04:40.8469 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 12:04:40.8636 Info Configuration initialized.
2024-11-14 12:24:23.6314 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 12:24:23.6377 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 12:24:23.6721 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:24:23.6721 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:24:23.6721 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 12:24:23.6721 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 12:24:23.6721 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 12:24:23.6721 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:24:23.6721 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:24:23.6721 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 12:24:23.6721 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 12:24:23.6902 Info Configuration initialized.
2024-11-14 12:25:36.5492 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 12:25:36.5492 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 12:25:36.5946 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:25:36.5946 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:25:36.5946 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 12:25:36.5946 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 12:25:36.5946 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 12:25:36.5946 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:25:36.5946 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:25:36.5946 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 12:25:36.6083 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 12:25:36.6083 Info Configuration initialized.
2024-11-14 12:28:54.7784 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 12:28:54.7844 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 12:28:54.8152 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:28:54.8152 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:28:54.8152 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 12:28:54.8152 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 12:28:54.8152 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 12:28:54.8152 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:28:54.8152 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:28:54.8235 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 12:28:54.8235 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 12:28:54.8235 Info Configuration initialized.
2024-11-14 12:41:32.7192 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 12:41:32.7271 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 12:41:32.7600 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:41:32.7600 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:41:32.7600 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 12:41:32.7600 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 12:41:32.7600 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 12:41:32.7600 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:41:32.7600 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:41:32.7600 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 12:41:32.7600 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 12:41:32.7770 Info Configuration initialized.
2024-11-14 12:42:02.6940 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 12:42:02.7031 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 12:42:02.7354 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:42:02.7354 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:42:02.7354 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 12:42:02.7413 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 12:42:02.7413 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 12:42:02.7413 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:42:02.7413 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:42:02.7413 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 12:42:02.7413 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 12:42:02.7413 Info Configuration initialized.
2024-11-14 12:45:11.5634 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 12:45:11.5698 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 12:45:11.6014 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:45:11.6014 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 12:45:11.6014 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 12:45:11.6014 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 12:45:11.6014 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 12:45:11.6014 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:45:11.6014 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 12:45:11.6014 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 12:45:11.6154 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 12:45:11.6154 Info Configuration initialized.
2024-11-14 13:44:25.2252 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 13:44:25.2252 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 13:44:25.2679 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:44:25.2679 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:44:25.2679 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 13:44:25.2679 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 13:44:25.2679 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 13:44:25.2679 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:44:25.2679 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:44:25.2679 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 13:44:25.2679 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 13:44:25.2850 Info Configuration initialized.
2024-11-14 13:45:07.4701 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 13:45:07.4762 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 13:45:07.5100 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:45:07.5100 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:45:07.5145 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 13:45:07.5145 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 13:45:07.5145 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 13:45:07.5145 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:45:07.5145 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:45:07.5145 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 13:45:07.5145 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 13:45:07.5145 Info Configuration initialized.
2024-11-14 13:45:49.6623 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 13:45:49.6683 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 13:45:49.7041 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:45:49.7041 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:45:49.7041 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 13:45:49.7041 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 13:45:49.7041 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 13:45:49.7041 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:45:49.7041 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:45:49.7041 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 13:45:49.7173 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 13:45:49.7173 Info Configuration initialized.
2024-11-14 13:51:45.2240 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 13:51:45.2321 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 13:51:45.2693 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:51:45.2693 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:51:45.2693 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 13:51:45.2693 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 13:51:45.2693 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 13:51:45.2785 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:51:45.2785 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:51:45.2785 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 13:51:45.2785 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 13:51:45.2785 Info Configuration initialized.
2024-11-14 13:52:30.3230 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 13:52:30.3290 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 13:52:30.3670 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:52:30.3670 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:52:30.3723 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 13:52:30.3723 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 13:52:30.3723 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 13:52:30.3723 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:52:30.3723 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:52:30.3723 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 13:52:30.3723 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 13:52:30.3723 Info Configuration initialized.
2024-11-14 13:53:05.9048 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 13:53:05.9136 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 13:53:05.9513 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:53:05.9513 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:53:05.9513 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 13:53:05.9513 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 13:53:05.9513 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 13:53:05.9513 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:53:05.9513 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:53:05.9513 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 13:53:05.9652 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 13:53:05.9652 Info Configuration initialized.
2024-11-14 13:53:46.5979 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 13:53:46.6061 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 13:53:46.6437 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:53:46.6437 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 13:53:46.6437 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 13:53:46.6437 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 13:53:46.6437 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 13:53:46.6518 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:53:46.6518 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 13:53:46.6518 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 13:53:46.6518 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 13:53:46.6518 Info Configuration initialized.
2024-11-14 14:00:57.0246 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 14:00:57.0309 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 14:00:57.0643 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 14:00:57.0643 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 14:00:57.0690 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 14:00:57.0690 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 14:00:57.0690 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 14:00:57.0690 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 14:00:57.0690 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 14:00:57.0690 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 14:00:57.0690 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 14:00:57.0690 Info Configuration initialized.
2024-11-14 14:23:12.6347 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 14:23:12.6347 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 14:23:12.6727 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 14:23:12.6727 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 14:23:12.6727 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 14:23:12.6727 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 14:23:12.6727 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 14:23:12.6727 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 14:23:12.6727 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 14:23:12.6727 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 14:23:12.6885 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 14:23:12.6885 Info Configuration initialized.
2024-11-14 14:32:29.4663 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 14:32:29.4726 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 14:32:29.5094 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 14:32:29.5094 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 14:32:29.5139 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 14:32:29.5139 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 14:32:29.5139 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 14:32:29.5139 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 14:32:29.5139 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 14:32:29.5139 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 14:32:29.5139 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 14:32:29.5281 Info Configuration initialized.
2024-11-14 16:50:22.3661 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 16:50:22.3726 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 16:50:22.4450 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 16:50:22.4450 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 16:50:22.4450 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 16:50:22.4507 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 16:50:22.4507 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 16:50:22.4507 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 16:50:22.4507 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 16:50:22.4507 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 16:50:22.4507 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 16:50:22.4507 Info Configuration initialized.
2024-11-14 16:51:07.2922 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-14 16:51:07.2987 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-14 16:51:07.3335 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 16:51:07.3335 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-14 16:51:07.3335 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-14 16:51:07.3335 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-14 16:51:07.3335 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-14 16:51:07.3416 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 16:51:07.3416 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-14 16:51:07.3416 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-14 16:51:07.3416 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-14 16:51:07.3416 Info Configuration initialized.
2024-11-15 09:00:49.0120 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-15 09:00:49.0248 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-15 09:00:49.0747 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 09:00:49.0747 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 09:00:49.0747 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-15 09:00:49.0747 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-15 09:00:49.0747 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-15 09:00:49.0747 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 09:00:49.0747 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 09:00:49.0747 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-15 09:00:49.0747 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-15 09:00:49.0932 Info Configuration initialized.
2024-11-15 09:01:13.7245 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-15 09:01:13.7308 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-15 09:01:13.7639 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 09:01:13.7639 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 09:01:13.7639 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-15 09:01:13.7639 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-15 09:01:13.7639 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-15 09:01:13.7639 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 09:01:13.7639 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 09:01:13.7639 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-15 09:01:13.7639 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-15 09:01:13.7825 Info Configuration initialized.
2024-11-15 09:17:00.3037 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-15 09:17:00.3109 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-15 09:17:00.3488 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 09:17:00.3488 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 09:17:00.3535 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-15 09:17:00.3535 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-15 09:17:00.3535 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-15 09:17:00.3535 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 09:17:00.3535 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 09:17:00.3535 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-15 09:17:00.3535 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-15 09:17:00.3674 Info Configuration initialized.
2024-11-15 09:30:21.2589 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-15 09:30:21.2649 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-15 09:30:21.3059 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 09:30:21.3059 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 09:30:21.3059 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-15 09:30:21.3059 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-15 09:30:21.3059 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-15 09:30:21.3059 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 09:30:21.3059 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 09:30:21.3059 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-15 09:30:21.3183 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-15 09:30:21.3183 Info Configuration initialized.
2024-11-15 16:46:05.9229 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-15 16:46:05.9292 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-15 16:46:05.9659 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 16:46:05.9659 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 16:46:05.9659 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-15 16:46:05.9659 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-15 16:46:05.9659 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-15 16:46:05.9659 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 16:46:05.9659 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 16:46:05.9659 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-15 16:46:05.9826 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-15 16:46:05.9826 Info Configuration initialized.
2024-11-15 16:50:59.6524 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-15 16:50:59.6597 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-15 16:50:59.7000 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 16:50:59.7000 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-15 16:50:59.7000 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-15 16:50:59.7000 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-15 16:50:59.7000 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-15 16:50:59.7000 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 16:50:59.7000 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-15 16:50:59.7136 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-15 16:50:59.7136 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-15 16:50:59.7136 Info Configuration initialized.
2024-11-18 11:39:02.5302 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 11:39:02.5388 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 11:39:02.5764 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 11:39:02.5764 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 11:39:02.5812 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 11:39:02.5812 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 11:39:02.5812 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 11:39:02.5812 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 11:39:02.5812 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 11:39:02.5812 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 11:39:02.5812 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 11:39:02.5956 Info Configuration initialized.
2024-11-18 11:41:06.6477 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 11:41:06.6477 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 11:41:06.6860 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 11:41:06.6860 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 11:41:06.6902 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 11:41:06.6902 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 11:41:06.6902 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 11:41:06.6902 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 11:41:06.6902 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 11:41:06.6902 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 11:41:06.6902 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 11:41:06.6902 Info Configuration initialized.
2024-11-18 11:44:42.3394 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 11:44:42.3454 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 11:44:42.3802 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 11:44:42.3802 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 11:44:42.3802 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 11:44:42.3802 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 11:44:42.3802 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 11:44:42.3802 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 11:44:42.3802 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 11:44:42.3802 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 11:44:42.3926 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 11:44:42.3926 Info Configuration initialized.
2024-11-18 15:45:13.3885 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 15:45:13.3885 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 15:45:13.4280 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 15:45:13.4280 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 15:45:13.4280 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 15:45:13.4280 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 15:45:13.4280 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 15:45:13.4280 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 15:45:13.4280 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 15:45:13.4280 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 15:45:13.4280 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 15:45:13.4448 Info Configuration initialized.
2024-11-18 15:51:02.8687 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 15:51:02.8778 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 15:51:02.9103 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 15:51:02.9103 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 15:51:02.9147 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 15:51:02.9147 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 15:51:02.9147 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 15:51:02.9147 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 15:51:02.9147 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 15:51:02.9147 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 15:51:02.9147 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 15:51:02.9286 Info Configuration initialized.
2024-11-18 15:54:41.7773 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 15:54:41.7773 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 15:54:41.8187 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 15:54:41.8187 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 15:54:41.8187 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 15:54:41.8187 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 15:54:41.8187 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 15:54:41.8187 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 15:54:41.8187 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 15:54:41.8187 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 15:54:41.8187 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 15:54:41.8440 Info Configuration initialized.
2024-11-18 15:59:01.5433 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 15:59:01.5433 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 15:59:01.5823 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 15:59:01.5823 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 15:59:01.5823 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 15:59:01.5823 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 15:59:01.5823 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 15:59:01.5823 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 15:59:01.5823 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 15:59:01.5823 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 15:59:01.5823 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 15:59:01.6003 Info Configuration initialized.
2024-11-18 16:11:36.0023 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 16:11:36.0023 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 16:11:36.0391 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:11:36.0391 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:11:36.0438 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 16:11:36.0438 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 16:11:36.0438 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 16:11:36.0438 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:11:36.0438 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:11:36.0438 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 16:11:36.0438 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 16:11:36.0438 Info Configuration initialized.
2024-11-18 16:14:55.1140 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 16:14:55.1201 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 16:14:55.1562 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:14:55.1562 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:14:55.1701 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 16:14:55.1701 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 16:14:55.1701 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 16:14:55.1701 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:14:55.1701 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:14:55.1891 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 16:14:55.1891 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 16:14:55.2025 Info Configuration initialized.
2024-11-18 16:22:33.1525 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 16:22:33.1525 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 16:22:33.1910 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:22:33.1910 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:22:33.1955 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 16:22:33.1955 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 16:22:33.1955 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 16:22:33.1955 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:22:33.1955 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:22:33.1955 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 16:22:33.1955 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 16:22:33.1955 Info Configuration initialized.
2024-11-18 16:29:07.7048 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 16:29:07.7110 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 16:29:07.7432 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:29:07.7432 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:29:07.7432 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 16:29:07.7432 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 16:29:07.7432 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 16:29:07.7432 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:29:07.7432 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:29:07.7432 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 16:29:07.7558 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 16:29:07.7558 Info Configuration initialized.
2024-11-18 16:38:33.6499 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 16:38:33.6499 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 16:38:33.6919 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:38:33.6919 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:38:33.6919 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 16:38:33.6919 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 16:38:33.6919 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 16:38:33.6919 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:38:33.6919 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:38:33.6919 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 16:38:33.6919 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 16:38:33.7089 Info Configuration initialized.
2024-11-18 16:52:47.9677 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 16:52:47.9736 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 16:52:48.0056 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:52:48.0056 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 16:52:48.0056 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 16:52:48.0056 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 16:52:48.0056 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 16:52:48.0056 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:52:48.0056 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 16:52:48.0056 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 16:52:48.0056 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 16:52:48.0225 Info Configuration initialized.
2024-11-18 17:02:04.8001 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 17:02:04.8061 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 17:02:04.8399 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:02:04.8399 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:02:04.8399 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 17:02:04.8465 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 17:02:04.8465 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 17:02:04.8465 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:02:04.8465 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:02:04.8465 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 17:02:04.8465 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 17:02:04.8465 Info Configuration initialized.
2024-11-18 17:04:44.2845 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 17:04:44.2920 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 17:04:44.3291 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:04:44.3304 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:04:44.3304 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 17:04:44.3304 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 17:04:44.3304 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 17:04:44.3304 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:04:44.3304 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:04:44.3304 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 17:04:44.3304 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 17:04:44.3484 Info Configuration initialized.
2024-11-18 17:06:05.9643 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 17:06:05.9643 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 17:06:06.0028 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:06:06.0028 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:06:06.0028 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 17:06:06.0028 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 17:06:06.0028 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 17:06:06.0028 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:06:06.0028 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:06:06.0028 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 17:06:06.0028 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 17:06:06.0201 Info Configuration initialized.
2024-11-18 17:17:42.6549 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 17:17:42.6610 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 17:17:42.6965 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:17:42.6977 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:17:42.6977 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 17:17:42.6977 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 17:17:42.6977 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 17:17:42.6977 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:17:42.6977 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:17:42.6977 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 17:17:42.6977 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 17:17:42.7170 Info Configuration initialized.
2024-11-18 17:18:19.6248 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 17:18:19.6248 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 17:18:19.6671 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:18:19.6671 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:18:19.6671 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 17:18:19.6671 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 17:18:19.6671 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 17:18:19.6671 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:18:19.6671 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:18:19.6671 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 17:18:19.6671 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 17:18:19.6851 Info Configuration initialized.
2024-11-18 17:29:22.6464 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 17:29:22.6528 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 17:29:22.6879 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:29:22.6879 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:29:22.6879 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 17:29:22.6879 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 17:29:22.6879 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 17:29:22.6954 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:29:22.6954 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:29:22.6954 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 17:29:22.6954 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 17:29:22.6954 Info Configuration initialized.
2024-11-18 17:31:26.5980 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 17:31:26.6044 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 17:31:26.6381 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:31:26.6381 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:31:26.6381 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 17:31:26.6381 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 17:31:26.6381 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 17:31:26.6488 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:31:26.6488 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:31:26.6488 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 17:31:26.6488 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 17:31:26.6488 Info Configuration initialized.
2024-11-18 17:31:46.5292 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 17:31:46.5292 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 17:31:46.5701 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:31:46.5701 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:31:46.5701 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 17:31:46.5701 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 17:31:46.5701 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 17:31:46.5701 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:31:46.5701 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:31:46.5701 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 17:31:46.5701 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 17:31:46.5880 Info Configuration initialized.
2024-11-18 17:47:14.1424 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 17:47:14.1483 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 17:47:14.1804 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:47:14.1804 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:47:14.1804 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 17:47:14.1804 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 17:47:14.1804 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 17:47:14.1804 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:47:14.1804 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:47:14.1804 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 17:47:14.1931 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 17:47:14.1931 Info Configuration initialized.
2024-11-18 17:52:29.4084 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-18 17:52:29.4145 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-18 17:52:29.4495 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:52:29.4495 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-18 17:52:29.4495 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-18 17:52:29.4495 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-18 17:52:29.4495 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-18 17:52:29.4576 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:52:29.4576 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-18 17:52:29.4576 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-18 17:52:29.4576 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-18 17:52:29.4576 Info Configuration initialized.
2024-11-19 09:07:05.9336 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-19 09:07:05.9395 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-19 09:07:05.9712 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-19 09:07:05.9712 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-19 09:07:05.9712 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-19 09:07:05.9712 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-19 09:07:05.9712 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-19 09:07:05.9712 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-19 09:07:05.9712 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-19 09:07:05.9712 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-19 09:07:05.9840 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-19 09:07:05.9840 Info Configuration initialized.
2024-11-19 09:10:26.3483 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-19 09:10:26.3548 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-19 09:10:26.3909 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-19 09:10:26.3909 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-19 09:10:26.3909 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-19 09:10:26.3961 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-19 09:10:26.3961 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-19 09:10:26.3961 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-19 09:10:26.3961 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-19 09:10:26.3961 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-19 09:10:26.3961 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-19 09:10:26.3961 Info Configuration initialized.
2024-11-19 11:44:26.1219 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-19 11:44:26.1279 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-19 11:44:26.1591 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-19 11:44:26.1591 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-19 11:44:26.1591 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-19 11:44:26.1591 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-19 11:44:26.1591 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-19 11:44:26.1591 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-19 11:44:26.1591 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-19 11:44:26.1591 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-19 11:44:26.1713 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Features\CreateCmd_main\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-19 11:44:26.1713 Info Configuration initialized.
2024-11-26 12:33:23.4354 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 12:33:23.4439 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 12:33:23.4897 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 12:33:23.4897 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 12:33:23.4897 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 12:33:23.4897 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 12:33:23.4897 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 12:33:23.4897 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 12:33:23.4897 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 12:33:23.5008 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 12:33:23.5008 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 12:33:23.5008 Info Configuration initialized.
2024-11-26 12:35:11.9311 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 12:35:11.9378 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 12:35:11.9760 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 12:35:11.9760 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 12:35:11.9760 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 12:35:11.9760 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 12:35:11.9760 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 12:35:11.9859 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 12:35:11.9859 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 12:35:11.9859 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 12:35:11.9859 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 12:35:12.0041 Info Configuration initialized.
2024-11-26 13:46:02.0269 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 13:46:02.0269 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 13:46:02.0733 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:46:02.0733 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:46:02.0733 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 13:46:02.0733 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 13:46:02.0733 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 13:46:02.0820 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:46:02.0820 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:46:02.0820 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 13:46:02.0820 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 13:46:02.0820 Info Configuration initialized.
2024-11-26 13:46:34.4549 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 13:46:34.4617 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 13:46:34.4959 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:46:34.4959 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:46:34.4959 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 13:46:34.4959 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 13:46:34.4959 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 13:46:34.5043 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:46:34.5043 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:46:34.5043 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 13:46:34.5043 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 13:46:34.5043 Info Configuration initialized.
2024-11-26 13:48:20.2342 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 13:48:20.2412 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 13:48:20.2774 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:48:20.2774 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:48:20.2774 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 13:48:20.2774 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 13:48:20.2774 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 13:48:20.2859 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:48:20.2859 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:48:20.2859 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 13:48:20.2859 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 13:48:20.2859 Info Configuration initialized.
2024-11-26 13:49:16.9709 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 13:49:16.9777 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 13:49:17.0141 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:49:17.0141 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:49:17.0191 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 13:49:17.0191 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 13:49:17.0191 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 13:49:17.0191 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:49:17.0191 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:49:17.0191 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 13:49:17.0191 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 13:49:17.0191 Info Configuration initialized.
2024-11-26 13:49:52.7521 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 13:49:52.7594 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 13:49:52.7977 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:49:52.7992 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:49:52.7992 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 13:49:52.7992 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 13:49:52.7992 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 13:49:52.7992 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:49:52.7992 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:49:52.7992 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 13:49:52.7992 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 13:49:52.8166 Info Configuration initialized.
2024-11-26 13:51:45.6822 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 13:51:45.6894 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 13:51:45.7230 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:51:45.7230 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:51:45.7230 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 13:51:45.7230 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 13:51:45.7230 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 13:51:45.7230 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:51:45.7230 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:51:45.7230 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 13:51:45.7358 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 13:51:45.7358 Info Configuration initialized.
2024-11-26 13:51:45.7878 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.0.log
2024-11-26 13:51:45.7878 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.0.log
2024-11-26 13:51:45.7878 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-INFOS.log to D:\logs\str.0.0.nlog-INFOS.0.log
2024-11-26 13:51:45.7878 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:51:45.8147 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.1.log
2024-11-26 13:51:45.8147 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.0.log
2024-11-26 13:51:45.8147 Info Roll archive D:\logs\str.0.0.nlog-INFOS.0.log to \logs\str.0.0.nlog-INFOS.1.log
2024-11-26 13:51:45.8147 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-INFOS.log to D:\logs\str.0.0.nlog-INFOS.0.log
2024-11-26 13:51:45.8147 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:51:45.8147 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:51:45.8147 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.2.log
2024-11-26 13:51:45.8147 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.1.log
2024-11-26 13:51:45.8147 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.0.log
2024-11-26 13:51:45.8147 Info Roll archive D:\logs\str.0.0.nlog-INFOS.1.log to \logs\str.0.0.nlog-INFOS.2.log
2024-11-26 13:51:45.8147 Info Roll archive D:\logs\str.0.0.nlog-INFOS.0.log to D:\logs\str.0.0.nlog-INFOS.1.log
2024-11-26 13:51:45.8147 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-INFOS.log to D:\logs\str.0.0.nlog-INFOS.0.log
2024-11-26 13:51:45.8147 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:51:45.8147 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:51:45.8147 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:51:45.8147 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.3.log
2024-11-26 13:51:45.8147 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.2.log
2024-11-26 13:51:45.8147 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.1.log
2024-11-26 13:51:45.8147 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.0.log
2024-11-26 13:51:52.7623 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.1.log
2024-11-26 13:51:52.7623 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.0.log
2024-11-26 13:51:52.7675 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:51:52.7675 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:51:52.7675 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:51:52.7675 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:51:52.7675 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:52:00.0496 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.2.log
2024-11-26 13:52:00.0496 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.1.log
2024-11-26 13:52:00.0496 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.0.log
2024-11-26 13:52:00.0496 Info Roll archive D:\logs\str.994.0.nlog-INFOS.0.log to \logs\str.994.0.nlog-INFOS.1.log
2024-11-26 13:52:00.0496 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:52:00.0496 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.4.log
2024-11-26 13:52:00.0496 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:52:00.0496 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:52:00.0496 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:52:00.0496 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:52:52.6386 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 13:52:52.6453 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 13:52:52.6814 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:52:52.6814 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:52:52.6814 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 13:52:52.6883 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 13:52:52.6883 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 13:52:52.6883 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:52:52.6883 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:52:52.6883 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 13:52:52.6883 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 13:52:52.6883 Info Configuration initialized.
2024-11-26 13:52:52.7516 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log
2024-11-26 13:52:52.7516 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:52:52.7516 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.0.log
2024-11-26 13:52:52.7516 Info Roll archive D:\logs\str.0.0.nlog-INFOS.2.log to \logs\str.0.0.nlog-INFOS.3.log
2024-11-26 13:52:52.7516 Info Roll archive D:\logs\str.0.0.nlog-INFOS.1.log to D:\logs\str.0.0.nlog-INFOS.2.log
2024-11-26 13:52:52.7516 Info Roll archive D:\logs\str.0.0.nlog-INFOS.0.log to D:\logs\str.0.0.nlog-INFOS.1.log
2024-11-26 13:52:52.7516 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-INFOS.log to D:\logs\str.0.0.nlog-INFOS.0.log
2024-11-26 13:52:52.7516 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:52:52.7516 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:52:52.7516 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.1.log
2024-11-26 13:52:52.7516 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.0.log
2024-11-26 13:52:52.7791 Info Roll archive D:\logs\str.0.0.nlog-INFOS.3.log to \logs\str.0.0.nlog-INFOS.4.log
2024-11-26 13:52:52.7791 Info Roll archive D:\logs\str.0.0.nlog-INFOS.2.log to D:\logs\str.0.0.nlog-INFOS.3.log
2024-11-26 13:52:52.7791 Info Roll archive D:\logs\str.0.0.nlog-INFOS.1.log to D:\logs\str.0.0.nlog-INFOS.2.log
2024-11-26 13:52:52.7791 Info Roll archive D:\logs\str.0.0.nlog-INFOS.0.log to D:\logs\str.0.0.nlog-INFOS.1.log
2024-11-26 13:52:52.7791 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-INFOS.log to D:\logs\str.0.0.nlog-INFOS.0.log
2024-11-26 13:52:52.7791 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:52:52.7791 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:52:52.7791 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:52:52.7835 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.2.log
2024-11-26 13:52:52.7835 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.1.log
2024-11-26 13:52:52.7835 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.0.log
2024-11-26 13:52:52.7835 Info Roll archive D:\logs\str.0.0.nlog-INFOS.4.log to \logs\str.0.0.nlog-INFOS.5.log
2024-11-26 13:52:52.7835 Info Roll archive D:\logs\str.0.0.nlog-INFOS.3.log to D:\logs\str.0.0.nlog-INFOS.4.log
2024-11-26 13:52:52.7835 Info Roll archive D:\logs\str.0.0.nlog-INFOS.2.log to D:\logs\str.0.0.nlog-INFOS.3.log
2024-11-26 13:52:52.7835 Info Roll archive D:\logs\str.0.0.nlog-INFOS.1.log to D:\logs\str.0.0.nlog-INFOS.2.log
2024-11-26 13:52:52.7835 Info Roll archive D:\logs\str.0.0.nlog-INFOS.0.log to D:\logs\str.0.0.nlog-INFOS.1.log
2024-11-26 13:52:52.7835 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-INFOS.log to D:\logs\str.0.0.nlog-INFOS.0.log
2024-11-26 13:52:52.7835 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:52:52.7835 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:52:52.7835 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:52:52.7835 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:52:52.7835 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.3.log
2024-11-26 13:52:52.7835 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.2.log
2024-11-26 13:52:52.7835 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.1.log
2024-11-26 13:52:52.7835 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.0.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-ALL.0.log
2024-11-26 13:52:59.7335 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log
2024-11-26 13:52:59.7384 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log
2024-11-26 13:52:59.7384 Info Roll archive D:\logs\str.994.0.nlog-INFOS.1.log to \logs\str.994.0.nlog-INFOS.2.log
2024-11-26 13:52:59.7384 Info Roll archive D:\logs\str.994.0.nlog-INFOS.0.log to D:\logs\str.994.0.nlog-INFOS.1.log
2024-11-26 13:52:59.7384 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.0.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:52:59.7384 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log
2024-11-26 13:52:59.7384 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:52:59.7384 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:52:59.7384 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:52:59.7384 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:53:07.0590 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log
2024-11-26 13:53:07.0590 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log
2024-11-26 13:53:07.0637 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log
2024-11-26 13:53:07.0637 Info Roll archive D:\logs\str.994.0.nlog-INFOS.2.log to \logs\str.994.0.nlog-INFOS.3.log
2024-11-26 13:53:07.0637 Info Roll archive D:\logs\str.994.0.nlog-INFOS.1.log to D:\logs\str.994.0.nlog-INFOS.2.log
2024-11-26 13:53:07.0637 Info Roll archive D:\logs\str.994.0.nlog-INFOS.0.log to D:\logs\str.994.0.nlog-INFOS.1.log
2024-11-26 13:53:07.0637 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:53:07.0637 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log
2024-11-26 13:53:07.0637 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log
2024-11-26 13:53:07.0637 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:53:07.0637 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:53:07.0637 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:53:07.0637 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:53:14.2461 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log
2024-11-26 13:53:14.2461 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log
2024-11-26 13:53:14.2461 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log
2024-11-26 13:53:14.2529 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log
2024-11-26 13:53:14.2529 Info Roll archive D:\logs\str.994.0.nlog-INFOS.3.log to \logs\str.994.0.nlog-INFOS.4.log
2024-11-26 13:53:14.2529 Info Roll archive D:\logs\str.994.0.nlog-INFOS.2.log to D:\logs\str.994.0.nlog-INFOS.3.log
2024-11-26 13:53:14.2529 Info Roll archive D:\logs\str.994.0.nlog-INFOS.1.log to D:\logs\str.994.0.nlog-INFOS.2.log
2024-11-26 13:53:14.2529 Info Roll archive D:\logs\str.994.0.nlog-INFOS.0.log to D:\logs\str.994.0.nlog-INFOS.1.log
2024-11-26 13:53:14.2529 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:53:14.2529 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log
2024-11-26 13:53:14.2529 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log
2024-11-26 13:53:14.2529 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log
2024-11-26 13:53:14.2529 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:53:14.2529 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:53:14.2529 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:53:14.2529 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:53:21.3694 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log
2024-11-26 13:53:21.3694 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log
2024-11-26 13:53:21.3694 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log
2024-11-26 13:53:21.3694 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log
2024-11-26 13:53:21.3694 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log
2024-11-26 13:53:21.3763 Info Roll archive D:\logs\str.994.0.nlog-INFOS.4.log to \logs\str.994.0.nlog-INFOS.5.log
2024-11-26 13:53:21.3763 Info Roll archive D:\logs\str.994.0.nlog-INFOS.3.log to D:\logs\str.994.0.nlog-INFOS.4.log
2024-11-26 13:53:21.3763 Info Roll archive D:\logs\str.994.0.nlog-INFOS.2.log to D:\logs\str.994.0.nlog-INFOS.3.log
2024-11-26 13:53:21.3763 Info Roll archive D:\logs\str.994.0.nlog-INFOS.1.log to D:\logs\str.994.0.nlog-INFOS.2.log
2024-11-26 13:53:21.3763 Info Roll archive D:\logs\str.994.0.nlog-INFOS.0.log to D:\logs\str.994.0.nlog-INFOS.1.log
2024-11-26 13:53:21.3763 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:53:21.3763 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log
2024-11-26 13:53:21.3763 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log
2024-11-26 13:53:21.3763 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log
2024-11-26 13:53:21.3763 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log
2024-11-26 13:53:21.3763 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:53:21.3763 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:53:21.3763 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:53:21.3763 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.5.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log
2024-11-26 13:53:28.4873 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log
2024-11-26 13:53:28.4873 Info Roll archive D:\logs\str.994.0.nlog-INFOS.5.log to \logs\str.994.0.nlog-INFOS.6.log
2024-11-26 13:53:28.4873 Info Roll archive D:\logs\str.994.0.nlog-INFOS.4.log to D:\logs\str.994.0.nlog-INFOS.5.log
2024-11-26 13:53:28.4873 Info Roll archive D:\logs\str.994.0.nlog-INFOS.3.log to D:\logs\str.994.0.nlog-INFOS.4.log
2024-11-26 13:53:28.4873 Info Roll archive D:\logs\str.994.0.nlog-INFOS.2.log to D:\logs\str.994.0.nlog-INFOS.3.log
2024-11-26 13:53:28.4873 Info Roll archive D:\logs\str.994.0.nlog-INFOS.1.log to D:\logs\str.994.0.nlog-INFOS.2.log
2024-11-26 13:53:28.4873 Info Roll archive D:\logs\str.994.0.nlog-INFOS.0.log to D:\logs\str.994.0.nlog-INFOS.1.log
2024-11-26 13:53:28.4873 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.8.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:53:28.4873 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:53:28.4873 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.6.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.5.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log
2024-11-26 13:53:35.6269 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log
2024-11-26 13:53:35.6269 Info Roll archive D:\logs\str.994.0.nlog-INFOS.6.log to \logs\str.994.0.nlog-INFOS.7.log
2024-11-26 13:53:35.6269 Info Roll archive D:\logs\str.994.0.nlog-INFOS.5.log to D:\logs\str.994.0.nlog-INFOS.6.log
2024-11-26 13:53:35.6269 Info Roll archive D:\logs\str.994.0.nlog-INFOS.4.log to D:\logs\str.994.0.nlog-INFOS.5.log
2024-11-26 13:53:35.6269 Info Roll archive D:\logs\str.994.0.nlog-INFOS.3.log to D:\logs\str.994.0.nlog-INFOS.4.log
2024-11-26 13:53:35.6269 Info Roll archive D:\logs\str.994.0.nlog-INFOS.2.log to D:\logs\str.994.0.nlog-INFOS.3.log
2024-11-26 13:53:35.6269 Info Roll archive D:\logs\str.994.0.nlog-INFOS.1.log to D:\logs\str.994.0.nlog-INFOS.2.log
2024-11-26 13:53:35.6269 Info Roll archive D:\logs\str.994.0.nlog-INFOS.0.log to D:\logs\str.994.0.nlog-INFOS.1.log
2024-11-26 13:53:35.6269 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.8.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.9.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.8.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:53:35.6269 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:53:35.6269 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:53:42.7586 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.6.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.7.log
2024-11-26 13:53:42.7586 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.6.log
2024-11-26 13:53:42.7586 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.5.log
2024-11-26 13:53:42.7586 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log
2024-11-26 13:53:42.7586 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log
2024-11-26 13:53:42.7586 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log
2024-11-26 13:53:42.7586 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log
2024-11-26 13:53:42.7586 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log
2024-11-26 13:53:42.7673 Info Roll archive D:\logs\str.994.0.nlog-INFOS.7.log to \logs\str.994.0.nlog-INFOS.8.log
2024-11-26 13:53:42.7673 Info Roll archive D:\logs\str.994.0.nlog-INFOS.6.log to D:\logs\str.994.0.nlog-INFOS.7.log
2024-11-26 13:53:42.7673 Info Roll archive D:\logs\str.994.0.nlog-INFOS.5.log to D:\logs\str.994.0.nlog-INFOS.6.log
2024-11-26 13:53:42.7673 Info Roll archive D:\logs\str.994.0.nlog-INFOS.4.log to D:\logs\str.994.0.nlog-INFOS.5.log
2024-11-26 13:53:42.7673 Info Roll archive D:\logs\str.994.0.nlog-INFOS.3.log to D:\logs\str.994.0.nlog-INFOS.4.log
2024-11-26 13:53:42.7673 Info Roll archive D:\logs\str.994.0.nlog-INFOS.2.log to D:\logs\str.994.0.nlog-INFOS.3.log
2024-11-26 13:53:42.7673 Info Roll archive D:\logs\str.994.0.nlog-INFOS.1.log to D:\logs\str.994.0.nlog-INFOS.2.log
2024-11-26 13:53:42.7673 Info Roll archive D:\logs\str.994.0.nlog-INFOS.0.log to D:\logs\str.994.0.nlog-INFOS.1.log
2024-11-26 13:53:42.7673 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:53:42.7673 Info FileTarget(Name=informationsallstructures): Deleting old archive file: 'D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.9.log'.
2024-11-26 13:53:42.7673 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.8.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.9.log
2024-11-26 13:53:42.7673 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.8.log
2024-11-26 13:53:42.7673 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log
2024-11-26 13:53:42.7673 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log
2024-11-26 13:53:42.7673 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log
2024-11-26 13:53:42.7673 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log
2024-11-26 13:53:42.7673 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:53:42.7673 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:53:42.7673 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:53:42.7673 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:53:49.8859 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.7.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.8.log
2024-11-26 13:53:49.8859 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.6.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.7.log
2024-11-26 13:53:49.8918 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.6.log
2024-11-26 13:53:49.8918 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.5.log
2024-11-26 13:53:49.8918 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log
2024-11-26 13:53:49.8918 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log
2024-11-26 13:53:49.8918 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log
2024-11-26 13:53:49.8918 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log
2024-11-26 13:53:49.8918 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log
2024-11-26 13:53:49.8918 Info Roll archive D:\logs\str.994.0.nlog-INFOS.8.log to \logs\str.994.0.nlog-INFOS.9.log
2024-11-26 13:53:49.8918 Info Roll archive D:\logs\str.994.0.nlog-INFOS.7.log to D:\logs\str.994.0.nlog-INFOS.8.log
2024-11-26 13:53:49.8918 Info Roll archive D:\logs\str.994.0.nlog-INFOS.6.log to D:\logs\str.994.0.nlog-INFOS.7.log
2024-11-26 13:53:49.8918 Info Roll archive D:\logs\str.994.0.nlog-INFOS.5.log to D:\logs\str.994.0.nlog-INFOS.6.log
2024-11-26 13:53:49.8918 Info Roll archive D:\logs\str.994.0.nlog-INFOS.4.log to D:\logs\str.994.0.nlog-INFOS.5.log
2024-11-26 13:53:49.8918 Info Roll archive D:\logs\str.994.0.nlog-INFOS.3.log to D:\logs\str.994.0.nlog-INFOS.4.log
2024-11-26 13:53:49.8918 Info Roll archive D:\logs\str.994.0.nlog-INFOS.2.log to D:\logs\str.994.0.nlog-INFOS.3.log
2024-11-26 13:53:49.8918 Info Roll archive D:\logs\str.994.0.nlog-INFOS.1.log to D:\logs\str.994.0.nlog-INFOS.2.log
2024-11-26 13:53:49.8918 Info Roll archive D:\logs\str.994.0.nlog-INFOS.0.log to D:\logs\str.994.0.nlog-INFOS.1.log
2024-11-26 13:53:49.8918 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:53:49.8918 Info FileTarget(Name=informationsallstructures): Deleting old archive file: 'D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.9.log'.
2024-11-26 13:53:49.8918 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.8.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.9.log
2024-11-26 13:53:49.8918 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.8.log
2024-11-26 13:53:49.8918 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log
2024-11-26 13:53:49.8918 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log
2024-11-26 13:53:49.8918 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log
2024-11-26 13:53:49.9076 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log
2024-11-26 13:53:49.9076 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:53:49.9076 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:53:49.9076 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:53:49.9076 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:53:57.0022 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.8.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.9.log
2024-11-26 13:53:57.0022 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.7.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.8.log
2024-11-26 13:53:57.0022 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.6.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.7.log
2024-11-26 13:53:57.0022 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.6.log
2024-11-26 13:53:57.0022 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.5.log
2024-11-26 13:53:57.0022 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log
2024-11-26 13:53:57.0022 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log
2024-11-26 13:53:57.0022 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log
2024-11-26 13:53:57.0177 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log
2024-11-26 13:53:57.0177 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log
2024-11-26 13:53:57.0177 Info FileTarget(Name=informationsbystructure): Deleting old archive file: 'D:\logs\str.994.0.nlog-INFOS.9.log'.
2024-11-26 13:53:57.0177 Info Roll archive D:\logs\str.994.0.nlog-INFOS.8.log to D:\logs\str.994.0.nlog-INFOS.9.log
2024-11-26 13:53:57.0177 Info Roll archive D:\logs\str.994.0.nlog-INFOS.7.log to D:\logs\str.994.0.nlog-INFOS.8.log
2024-11-26 13:53:57.0177 Info Roll archive D:\logs\str.994.0.nlog-INFOS.6.log to D:\logs\str.994.0.nlog-INFOS.7.log
2024-11-26 13:53:57.0177 Info Roll archive D:\logs\str.994.0.nlog-INFOS.5.log to D:\logs\str.994.0.nlog-INFOS.6.log
2024-11-26 13:53:57.0177 Info Roll archive D:\logs\str.994.0.nlog-INFOS.4.log to D:\logs\str.994.0.nlog-INFOS.5.log
2024-11-26 13:53:57.0177 Info Roll archive D:\logs\str.994.0.nlog-INFOS.3.log to D:\logs\str.994.0.nlog-INFOS.4.log
2024-11-26 13:53:57.0177 Info Roll archive D:\logs\str.994.0.nlog-INFOS.2.log to D:\logs\str.994.0.nlog-INFOS.3.log
2024-11-26 13:53:57.0177 Info Roll archive D:\logs\str.994.0.nlog-INFOS.1.log to D:\logs\str.994.0.nlog-INFOS.2.log
2024-11-26 13:53:57.0177 Info Roll archive D:\logs\str.994.0.nlog-INFOS.0.log to D:\logs\str.994.0.nlog-INFOS.1.log
2024-11-26 13:53:57.0177 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:53:57.0177 Info FileTarget(Name=informationsallstructures): Deleting old archive file: 'D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.9.log'.
2024-11-26 13:53:57.0325 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.8.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.9.log
2024-11-26 13:53:57.0325 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.8.log
2024-11-26 13:53:57.0325 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log
2024-11-26 13:53:57.0325 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log
2024-11-26 13:53:57.0325 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log
2024-11-26 13:53:57.0325 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log
2024-11-26 13:53:57.0325 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:53:57.0325 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:53:57.0325 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:53:57.0325 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:54:04.1338 Info FileTarget(Name=alltracesbystructure): Deleting old archive file: 'D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.9.log'.
2024-11-26 13:54:04.1338 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.8.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.9.log
2024-11-26 13:54:04.1338 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.7.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.8.log
2024-11-26 13:54:04.1338 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.6.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.7.log
2024-11-26 13:54:04.1429 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.6.log
2024-11-26 13:54:04.1429 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.5.log
2024-11-26 13:54:04.1429 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.4.log
2024-11-26 13:54:04.1429 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.3.log
2024-11-26 13:54:04.1429 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.2.log
2024-11-26 13:54:04.1429 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.1.log
2024-11-26 13:54:04.1429 Info FileTarget(Name=alltracesbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.994.0.nlog-ALL.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-ALL.0.log
2024-11-26 13:54:04.1573 Info FileTarget(Name=informationsbystructure): Deleting old archive file: 'D:\logs\str.994.0.nlog-INFOS.9.log'.
2024-11-26 13:54:04.1573 Info Roll archive D:\logs\str.994.0.nlog-INFOS.8.log to D:\logs\str.994.0.nlog-INFOS.9.log
2024-11-26 13:54:04.1573 Info Roll archive D:\logs\str.994.0.nlog-INFOS.7.log to D:\logs\str.994.0.nlog-INFOS.8.log
2024-11-26 13:54:04.1573 Info Roll archive D:\logs\str.994.0.nlog-INFOS.6.log to D:\logs\str.994.0.nlog-INFOS.7.log
2024-11-26 13:54:04.1573 Info Roll archive D:\logs\str.994.0.nlog-INFOS.5.log to D:\logs\str.994.0.nlog-INFOS.6.log
2024-11-26 13:54:04.1573 Info Roll archive D:\logs\str.994.0.nlog-INFOS.4.log to D:\logs\str.994.0.nlog-INFOS.5.log
2024-11-26 13:54:04.1573 Info Roll archive D:\logs\str.994.0.nlog-INFOS.3.log to D:\logs\str.994.0.nlog-INFOS.4.log
2024-11-26 13:54:04.1573 Info Roll archive D:\logs\str.994.0.nlog-INFOS.2.log to D:\logs\str.994.0.nlog-INFOS.3.log
2024-11-26 13:54:04.1573 Info Roll archive D:\logs\str.994.0.nlog-INFOS.1.log to D:\logs\str.994.0.nlog-INFOS.2.log
2024-11-26 13:54:04.1573 Info Roll archive D:\logs\str.994.0.nlog-INFOS.0.log to D:\logs\str.994.0.nlog-INFOS.1.log
2024-11-26 13:54:04.1573 Info FileTarget(Name=informationsbystructure): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.994.0.nlog-INFOS.log to D:\logs\str.994.0.nlog-INFOS.0.log
2024-11-26 13:54:04.1573 Info FileTarget(Name=informationsallstructures): Deleting old archive file: 'D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.9.log'.
2024-11-26 13:54:04.1573 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.8.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.9.log
2024-11-26 13:54:04.1732 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.8.log
2024-11-26 13:54:04.1732 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.7.log
2024-11-26 13:54:04.1732 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.6.log
2024-11-26 13:54:04.1732 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.5.log
2024-11-26 13:54:04.1732 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.4.log
2024-11-26 13:54:04.1732 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.3.log
2024-11-26 13:54:04.1732 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.2.log
2024-11-26 13:54:04.1732 Info Roll archive D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.1.log
2024-11-26 13:54:04.1732 Info FileTarget(Name=informationsallstructures): Archiving D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\str.ALL.0.nlog-INFOS.log to D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\logs\archive\str.ALL.0.nlog-INFOS.0.log
2024-11-26 13:54:47.9372 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 13:54:47.9440 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 13:54:47.9790 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:54:47.9790 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 13:54:47.9790 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 13:54:47.9851 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 13:54:47.9851 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 13:54:47.9851 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:54:47.9851 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 13:54:47.9851 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 13:54:47.9851 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 13:54:47.9851 Info Configuration initialized.
2024-11-26 14:03:55.5005 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 14:03:55.5072 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 14:03:55.5452 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 14:03:55.5452 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 14:03:55.5452 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 14:03:55.5452 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 14:03:55.5452 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 14:03:55.5452 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 14:03:55.5452 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 14:03:55.5452 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 14:03:55.5452 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 14:03:55.5628 Info Configuration initialized.
2024-11-26 14:05:11.4214 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 14:05:11.4280 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 14:05:11.4627 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 14:05:11.4627 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 14:05:11.4675 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 14:05:11.4675 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 14:05:11.4675 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 14:05:11.4675 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 14:05:11.4675 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 14:05:11.4675 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 14:05:11.4675 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 14:05:11.4834 Info Configuration initialized.
2024-11-26 14:13:13.8538 Info Loading assembly name: NLog.Web.AspNetCore
2024-11-26 14:13:13.8609 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2024-11-26 14:13:13.9014 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 14:13:13.9014 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2024-11-26 14:13:13.9014 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2024-11-26 14:13:13.9014 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2024-11-26 14:13:13.9014 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2024-11-26 14:13:13.9014 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 14:13:13.9014 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2024-11-26 14:13:13.9014 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2024-11-26 14:13:13.9014 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2024-11-26 14:13:13.9213 Info Configuration initialized.
2025-01-23 14:40:02.1483 Info Loading assembly name: NLog.Web.AspNetCore
2025-01-23 14:40:02.1568 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Error loading extensions: NLog.Web.AspNetCore
 ---> System.IO.FileNotFoundException: Could not load file or assembly 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'. Le fichier spécifié est introuvable.
File name: 'NLog.Web.AspNetCore, Culture=neutral, PublicKeyToken=null'
   at System.Reflection.RuntimeAssembly.InternalLoad(AssemblyName assemblyName, StackCrawlMark& stackMark, AssemblyLoadContext assemblyLoadContext, RuntimeAssembly requestingAssembly, Boolean throwOnFileNotFound)
   at System.Reflection.Assembly.Load(String assemblyString)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(String assemblyName)
   at NLog.Config.AssemblyExtensionLoader.LoadAssemblyFromName(ConfigurationItemFactory factory, String assemblyName, String itemNamePrefix)
   at NLog.Config.LoggingConfigurationParser.ParseExtensionWithAssemblyName(String assemblyName, String prefix)
   --- End of inner exception stack trace ---
2025-01-23 14:40:02.2009 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'aspnet-request-ip' - Extension NLog.Web.AspNetCore not included?
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2025-01-23 14:40:02.2009 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Failed to create LayoutRenderer with unknown type-alias: 'version' - Verify type-alias and check extension is included.
   at NLog.Config.FactoryExtensions.CreateInstance[TBaseType](IFactory`1 factory, String typeAlias)
   at NLog.Layouts.LayoutParser.GetLayoutRenderer(String typeName, ConfigurationItemFactory configurationItemFactory, Nullable`1 throwConfigExceptions)
2025-01-23 14:40:02.2009 Info Registered target NLog.Targets.FileTarget(Name=alltracesbystructure)
2025-01-23 14:40:02.2009 Info Registered target NLog.Targets.FileTarget(Name=informationsbystructure)
2025-01-23 14:40:02.2009 Info Registered target NLog.Targets.FileTarget(Name=informationsallstructures)
2025-01-23 14:40:02.2101 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'lifetimeConsole' not found for logging rule: Microsoft.Hosting.Lifetime.
2025-01-23 14:40:02.2101 Warn Error has been raised. Exception: NLog.NLogConfigurationException: Target 'ownFile-web' not found for logging rule: Microsoft.Hosting.Lifetime.
2025-01-23 14:40:02.2101 Info NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c. File version: 5.3.4.2778. Product version: 5.3.4+73d83d3161d31288ca5c554cc50d27b6bed5f28b. GlobalAssemblyCache: False
2025-01-23 14:40:02.2101 Info Validating config: TargetNames=alltracesbystructure, informationsbystructure, informationsallstructures, ConfigItems=133, FilePath=D:\WORK\Themis_core_DEV\Applis\ServiceRattrapPanier\bin\Debug\net8.0\NLog.config
2025-01-23 14:40:02.2101 Info Configuration initialized.
