﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35327.3
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ServiceRattrapPanier", "ServiceRattrapPanier.csproj", "{5D72EDC7-53E4-456D-B635-06273A7C2DBB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLL", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL.csproj", "{D0B8B3D9-FD49-4DA9-AB50-D6BF33E2DD1F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLLTests", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLLTests\Core.Themis.Libraries.BLLTests.csproj", "{7E52E882-C297-4586-A43E-02CFAF32C860}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Data", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Core.Themis.Libraries.Data.csproj", "{754B2999-557E-4384-B3BA-8B9DB3F45E44}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DataTests", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Libraries.DataTests\Core.Themis.Libraries.DataTests.csproj", "{68E20639-39F7-4038-B3F6-640D3BA2732B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Utilities", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj", "{F1FC6582-5843-4003-907B-0F71AAFF874C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.UtilitiesTests", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.UtilitiesTests\Core.Themis.Libraries.UtilitiesTests.csproj", "{278E2A3A-035B-4B39-B321-EE14105A5849}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DTO", "..\..\Libraries\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj", "{456FDA42-F137-4C3C-B8CF-E70308867522}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5D72EDC7-53E4-456D-B635-06273A7C2DBB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D72EDC7-53E4-456D-B635-06273A7C2DBB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D72EDC7-53E4-456D-B635-06273A7C2DBB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D72EDC7-53E4-456D-B635-06273A7C2DBB}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0B8B3D9-FD49-4DA9-AB50-D6BF33E2DD1F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0B8B3D9-FD49-4DA9-AB50-D6BF33E2DD1F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0B8B3D9-FD49-4DA9-AB50-D6BF33E2DD1F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0B8B3D9-FD49-4DA9-AB50-D6BF33E2DD1F}.Release|Any CPU.Build.0 = Release|Any CPU
		{7E52E882-C297-4586-A43E-02CFAF32C860}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7E52E882-C297-4586-A43E-02CFAF32C860}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7E52E882-C297-4586-A43E-02CFAF32C860}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7E52E882-C297-4586-A43E-02CFAF32C860}.Release|Any CPU.Build.0 = Release|Any CPU
		{754B2999-557E-4384-B3BA-8B9DB3F45E44}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{754B2999-557E-4384-B3BA-8B9DB3F45E44}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{754B2999-557E-4384-B3BA-8B9DB3F45E44}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{754B2999-557E-4384-B3BA-8B9DB3F45E44}.Release|Any CPU.Build.0 = Release|Any CPU
		{68E20639-39F7-4038-B3F6-640D3BA2732B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{68E20639-39F7-4038-B3F6-640D3BA2732B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{68E20639-39F7-4038-B3F6-640D3BA2732B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{68E20639-39F7-4038-B3F6-640D3BA2732B}.Release|Any CPU.Build.0 = Release|Any CPU
		{F1FC6582-5843-4003-907B-0F71AAFF874C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F1FC6582-5843-4003-907B-0F71AAFF874C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F1FC6582-5843-4003-907B-0F71AAFF874C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F1FC6582-5843-4003-907B-0F71AAFF874C}.Release|Any CPU.Build.0 = Release|Any CPU
		{278E2A3A-035B-4B39-B321-EE14105A5849}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{278E2A3A-035B-4B39-B321-EE14105A5849}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{278E2A3A-035B-4B39-B321-EE14105A5849}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{278E2A3A-035B-4B39-B321-EE14105A5849}.Release|Any CPU.Build.0 = Release|Any CPU
		{456FDA42-F137-4C3C-B8CF-E70308867522}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{456FDA42-F137-4C3C-B8CF-E70308867522}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{456FDA42-F137-4C3C-B8CF-E70308867522}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{456FDA42-F137-4C3C-B8CF-E70308867522}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {974B12E9-4544-431A-86D2-ED3ED6E22E2B}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 9
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://v-dev-test:8080/tfs/appscollection
		SccLocalPath0 = .
		SccProjectUniqueName1 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj
		SccProjectName1 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLL
		SccLocalPath1 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL
		SccProjectUniqueName2 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests\\Core.Themis.Libraries.BLLTests.csproj
		SccProjectName2 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLLTests
		SccLocalPath2 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests
		SccProjectUniqueName3 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj
		SccProjectName3 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Librairies.Data
		SccLocalPath3 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data
		SccProjectUniqueName4 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests\\Core.Themis.Libraries.DataTests.csproj
		SccProjectName4 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Libraries.DataTests
		SccLocalPath4 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests
		SccProjectUniqueName5 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj
		SccProjectName5 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.Utilities
		SccLocalPath5 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities
		SccProjectUniqueName6 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests\\Core.Themis.Libraries.UtilitiesTests.csproj
		SccProjectName6 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.UtilitiesTests
		SccLocalPath6 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests
		SccProjectUniqueName7 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj
		SccProjectName7 = ../../Libraries/Core.Themis.Libraries.DTO
		SccLocalPath7 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO
		SccProjectUniqueName8 = ServiceRattrapPanier.csproj
		SccLocalPath8 = .
	EndGlobalSection
EndGlobal
