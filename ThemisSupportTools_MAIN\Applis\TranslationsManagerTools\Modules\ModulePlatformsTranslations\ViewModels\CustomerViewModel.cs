﻿using Microsoft.Extensions.Configuration;
using Prism.Commands;
using Prism.Mvvm;
using Prism.Regions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Xml.Linq;
using TranslationsManagerTools.Services.Interfaces;

namespace ModulePlatformsTranslations.ViewModels
{
    public class CustomerViewModel : BindableBase, INavigationAware
    {
        //Appel du fichier de config appsettings.json
        private readonly IConfiguration _configuration;
        private readonly IXmlFileService _xmlFileService;
        /// <summary>
        /// Liste des langues possibles pour les traductions (récupère la langue des fichiers par défaut)
        /// </summary>
        private ObservableCollection<string> translationLanguages = new ObservableCollection<string>();
        public ObservableCollection<string> TranslationLanguages
        {
            get => translationLanguages;
            set => SetProperty(ref translationLanguages, value);
        }

        /// <summary>
        /// Liste des termes du fichier xml
        /// </summary>
        private ObservableCollection<XElement> defaultXmlContent = new ObservableCollection<XElement>();
        public ObservableCollection<XElement> DefaultXmlContent
        {
            get => defaultXmlContent;
            set => SetProperty(ref defaultXmlContent, value);
        }

        /// <summary>
        /// Nombre d'élements dans le fichier xml
        /// </summary>
        private int nbTranslationTerm;
        public int NbTranslationTerm
        {
            get => nbTranslationTerm;
            set => SetProperty(ref nbTranslationTerm, value);
        }



        /// <summary>
        /// Code de la langue sélectionnée
        /// </summary>
        private string translationLanguageSelected;
        public string TranslationLanguageSelected
        {
            get => translationLanguageSelected;
            set => SetProperty(ref translationLanguageSelected, value);
        }

        /// <summary>
        /// Affiche ou masque les clés vide dans le fichier xml (suivant la langue sélectionnée)
        /// </summary>
        private bool isShowEmptyKeys;
        public bool IsShowEmptyKeys
        {
            get => isShowEmptyKeys;
            set => SetProperty(ref isShowEmptyKeys, value);
        }

        #region Commandes


        /// <summary>
        /// Chargement de la liste des traductions
        /// </summary>
        private void LoadDefaultTranslation()
        {

            if (!string.IsNullOrEmpty(TranslationLanguageSelected))
            {

                string platformDirectory = _configuration["CustomerPlatformName"];
                string defaultFile = _configuration["TranslationsPlatfoms"].Replace("[platformName\\]", platformDirectory + "\\").Replace("[.lang]", "." + TranslationLanguageSelected);

                List<XElement> defaultXmlContent = _xmlFileService.LoadXmlFile(defaultFile);

                if (IsShowEmptyKeys)
                {
                    var filteredEmptyTerms = defaultXmlContent.Where(xe => string.IsNullOrEmpty(xe.Value)).ToList();
                    DefaultXmlContent = new ObservableCollection<XElement>(filteredEmptyTerms);
                }
                else
                {
                    DefaultXmlContent = new ObservableCollection<XElement>();
                    DefaultXmlContent = new ObservableCollection<XElement>(defaultXmlContent);

                }

                NbTranslationTerm = DefaultXmlContent.Count();
            }

        }

        private DelegateCommand compareKeys;
        public DelegateCommand CompareKeys =>
          compareKeys ?? (compareKeys = new DelegateCommand(ExecuteCompareKeys));

        /// <summary>
        /// Bouton permettant d'exporter les clés différentes du fichier xml
        /// </summary>
        void ExecuteCompareKeys()
        {
            if (string.IsNullOrEmpty(TranslationLanguageSelected))
            {
                MessageBox.Show("Merci de sélectionner une langue pour comparer", "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            else
            {
                string platformDirectory = _configuration["CustomerPlatformName"];
                string defaultFrenchFile = _configuration["TranslationsPlatfoms"].Replace("[platformName\\]", platformDirectory + "\\").Replace("[.lang]", ".fr");
                string defaultLanguageFile = _configuration["TranslationsPlatfoms"].Replace("[platformName\\]", platformDirectory + "\\").Replace("[.lang]", "." + TranslationLanguageSelected);

                if (!string.IsNullOrEmpty(defaultLanguageFile) && !string.IsNullOrEmpty(defaultFrenchFile))
                {
                    string xmlPathToSave = _configuration["XmlPathToSaveCustomer"].ToString().Replace("[lang]", TranslationLanguageSelected);
                    var elementsResult = _xmlFileService.CompareXmlFile(defaultFrenchFile, defaultLanguageFile);
                    if (elementsResult.Count > 0)
                    {

                        if (!string.IsNullOrEmpty(xmlPathToSave))
                        {
                            XElement root = XElement.Parse(@$"<Root> {string.Concat(elementsResult)} </Root>");

                            if (!Directory.Exists(Path.GetDirectoryName(xmlPathToSave)))
                            {
                                Directory.CreateDirectory(Path.GetDirectoryName(xmlPathToSave));
                            }

                            root.Save(xmlPathToSave);
                            MessageBox.Show($"Un fichier à été générer avec {elementsResult.Count} clés différentes {xmlPathToSave}", "Comparer", MessageBoxButton.OK, MessageBoxImage.Information);
                            //Process.Start("notepad.exe", xmlPathToSave);
                        }
                        else
                        {
                            MessageBox.Show("Le chemin d'export n'est pas défini", "Export", MessageBoxButton.OK, MessageBoxImage.Error);
                        }

                    }
                    else
                    {
                        MessageBox.Show("Les fichiers sont les mêmes", "Comparer", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
        }

        private DelegateCommand showEmptyKeysCommand;
        public DelegateCommand ShowEmptyKeysCommand =>
          showEmptyKeysCommand ?? (showEmptyKeysCommand = new DelegateCommand(ExecuteShowEmptyKeysCommand));

        /// <summary>
        /// Checkbox qui permet d'afficher ou masquer les termes du fichier xml suivant la langue 
        /// </summary>
        void ExecuteShowEmptyKeysCommand()
        {
            //Charge la liste complète des termes de traductions
            LoadDefaultTranslation();
        }



        private DelegateCommand<object> selectedItemChangedCommand;
        public DelegateCommand<object> SelectedItemChangedCommand =>
          selectedItemChangedCommand ?? (selectedItemChangedCommand = new DelegateCommand<object>(ExecuteSelectedItemChangedCommand));

        /// <summary>
        /// Lorsque l'on sélectionne un élément dans la liste des langues
        /// </summary>
        /// <param name="obj">l'élément sélectionné (xelement)</param>
        void ExecuteSelectedItemChangedCommand(object obj)
        {
            if (obj != null)
            {
                string lang = (string)obj;
                if (!string.IsNullOrEmpty(lang))
                {

                    LoadDefaultTranslation();
                }
            }
        }


        private DelegateCommand<object> updateTermCommand;
        public DelegateCommand<object> UpdateTermCommand =>
          updateTermCommand ?? (updateTermCommand = new DelegateCommand<object>(ExecuteUpdateTermCommand));

        /// <summary>
        /// Bouton permettant de mettre à jour un terme du fichier xml
        /// </summary>
        /// <param name="obj">l'élément sélectionné (xelement)</param>
        void ExecuteUpdateTermCommand(object obj)
        {
            if (obj != null)
            {
                string platformDirectory = _configuration["CustomerPlatformName"];
                string defaultFile = _configuration["TranslationsPlatfoms"].Replace("[platformName\\]", platformDirectory + "\\").Replace("[.lang]", "." + TranslationLanguageSelected);

                var elementToUpdate = obj as XElement;

                _xmlFileService.UpdateXelement(defaultFile, elementToUpdate);
            }
        }
        #endregion



        public CustomerViewModel(IXmlFileService xmlFileService, IConfiguration configuration)
        {
            _xmlFileService = xmlFileService;
            _configuration = configuration;
        }

        /// <summary>
        /// Rempli la liste des langues
        /// </summary>
        private void GetPlatformDefaultLangages()
        {
            string platformDirectory = _configuration["CustomerPlatformName"];
            string defaultDirectory = _configuration["TranslationsPlatfoms"].Replace("[platformName\\]", platformDirectory + "\\");

            var files = Directory.EnumerateFiles(Path.GetDirectoryName(defaultDirectory));

            List<string> languges = new List<string>();
            foreach (var item in files)
            {
                languges.Add(Path.GetFileName(item).Split('.')[1]);
            }

            TranslationLanguages = new ObservableCollection<string>(languges);
        }


        public void OnNavigatedTo(NavigationContext navigationContext)
        {
            GetPlatformDefaultLangages();
        }

        public bool IsNavigationTarget(NavigationContext navigationContext)
        {
            return true;
        }

        public void OnNavigatedFrom(NavigationContext navigationContext)
        {
        }
    }
}
