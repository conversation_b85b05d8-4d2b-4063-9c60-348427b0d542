﻿using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Core.Themis.API.Offers.Controllers
{
    [ApiController]
    public class SponsorController : Controller
    {
        private readonly IConfiguration _configuration;
        private readonly ISponsorManager _sponsorManager;
        private readonly IBasketManager _basketManager;

        private static readonly RodrigueNLogger Logger = new();

        public SponsorController(
            IConfiguration configuration,
            ISponsorManager sponsorManager,
            IBasketManager basketManager)
        {
            _configuration = configuration;
            _sponsorManager = sponsorManager;
            _basketManager = basketManager;
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<SponsorPaymentDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        //[ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpGet]
        // [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/{basketId}/sponsor")]
        public IActionResult Sponsor(int structureId, int basketId)
        {
            List<SponsorPaymentDTO> sponP = _sponsorManager.GetSponsorsAmountByBasketId(structureId, basketId);
            return Ok(sponP);
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(int))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        //[ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpGet]
        // [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/{basketId}/sponsorAmount")]
        public IActionResult SponsorAmount(int structureId, int basketId)
        {
            List<SponsorPaymentDTO> sponP = _sponsorManager.GetSponsorsAmountByBasketId(structureId, basketId);
            var amount = sponP.SelectMany(sp => sp.TypeTarifs).Sum(tt => tt.ValeurTarif);
            return Ok(amount);
        }


        /// <summary>
        /// push toutes les utilisations de cartes sponsors non envoyées
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(int))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        //[ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpPost]
        // [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/sponsor/post/all")]
        public IActionResult SponsorPush(int structureId)
        {
            int n = _sponsorManager.PushSponsorsCardsToSponsor(structureId);
            return Ok(n);
        }


        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(bool))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpGet]
        [Route("api/{structureId}/{basketId}/checkSponsorRemaining")]
        public IActionResult CheckSponsorRemaining(int structureId, int basketId)
        {
            int sponsorRemaining = -1;
            var basket = _basketManager.GetBasketById(structureId, basketId);

            //Liste de Gestion_place lié au sponsorRéférence pour ce panier
            var gestionPlaceSponsorReferenceOfMyBasket = basket.ListAllSeatsUnitSales().Select(s => new Tuple<int, int, string>(s.RuleId,s.SessionId, s.SponsorReference )).ToList();

         
            var sponsorOfOtherBasket = _sponsorManager.GetSponsorReferenceOfOtherBasket(structureId, basketId, gestionPlaceSponsorReferenceOfMyBasket);


            //Liste de Gestion_place lié au sponsorRéférence pour les autres paniers
            var gestionPlaceSponsorReferenceOfOtherBasket = sponsorOfOtherBasket.Select(sp => new Tuple<int, int, string>(sp.PanierEntree.GestionPlaceId.Value, sp.PanierEntree.SeanceId, sp.ReferenceSponsor)).ToList();

            var gestionPlaceSponsorReferencefusion = gestionPlaceSponsorReferenceOfMyBasket.Concat(gestionPlaceSponsorReferenceOfOtherBasket);

            return Ok(sponsorRemaining >= 0);
        }

    }
}
