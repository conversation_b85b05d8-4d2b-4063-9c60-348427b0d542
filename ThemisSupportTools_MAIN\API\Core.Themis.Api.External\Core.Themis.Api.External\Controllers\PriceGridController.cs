﻿using AutoMapper;
using Core.Themis.Api.External.Helpers;
using Core.Themis.Api.External.Helpers.Interfaces;
using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.adhesion_offres;
using Core.Themis.Libraries.BLL.adhesion_offres.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.adhesion_offres;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.Orders;
using Core.Themis.Libraries.DTO.WTObjects;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using log4net.Core;
using log4net.Plugin;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using RestSharp;
using System.Data;
using System.Net;

namespace Core.Themis.Api.External.Controllers
{
    /// <summary>
    /// Price grid 
    /// </summary>
    [ApiController]

    [ApiExplorerSettings(GroupName = "ext")]
    public class PriceGridController : ControllerBase
    {

        private static readonly RodrigueNLogger Logger = new RodrigueNLogger();

        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IBuyerProfilManager _buyerProfilManager;
        private readonly IAdhesionCatalogManager _adhesionCatalogManager;
        private readonly IBasketManager _basketManager;
        private readonly IEventManager _eventManager;
        private readonly IPriceManager _priceManager;
        private readonly IDeviseManager _deviseManager;

        private readonly IApiOffersClient _apiOffersClients;
        private IMapper _mapper
        {
            get;
        }

        public PriceGridController(IConfiguration configuration, IMemoryCache memoryCache, IMapper mapper, 
                        IApiOffersClient apiOffersClient,
                        IBuyerProfilManager buyerProfilManager,
                        IAdhesionCatalogManager adhesionCatalogManager,
                        IBasketManager basketManager,
                        IEventManager eventManager,
                        IPriceManager priceManager,
                        IDeviseManager deviseManager

            )
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _mapper = mapper;
            _apiOffersClients = apiOffersClient;
            _buyerProfilManager = buyerProfilManager;
            _adhesionCatalogManager = adhesionCatalogManager;
            _eventManager = eventManager;
            _basketManager = basketManager;
            _priceManager = priceManager;
            _deviseManager = deviseManager;
        }

        /// <summary>
        /// Get price grid of the event, for a buyer profil / reseller
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <param name="eventId">The event identifier</param>
        /// <param name="identityId">The customer identifier</param>
        /// <param name="webUserId">The web user identifier</param>
        /// <param name="buyerProfilId">The buyer profil identifier</param>
        /// <returns></returns>

        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Event))]
        [HttpGet]
        [Route("api/{structureId}/Sessions/{eventId}/{langCode}/{identityId}/{webUserId}/{buyerProfilId}")]
        [Authorize(Roles = "Integrateur,Revendeur,Admin")]
        public IActionResult LoadSessions(int structureId, string langCode, int eventId, int identityId, int webUserId, int buyerProfilId)
        {

            return LoadSessionsP(structureId, langCode, eventId, 0, identityId, webUserId, buyerProfilId, "", "");
        }

        /// <summary>
        /// Get price grid of the event for an identity
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <param name="eventId">The event identifier</param>
        /// <param name="identityId">The customer identifier</param>
        /// <returns></returns>
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Event))]
        [HttpGet]
        [Route("api/{structureId}/Sessions/{eventId}/{langCode}")]
        [Route("api/{structureId}/Sessions/{eventId}/{langCode}/{identityId}")]
        [Authorize(Roles = "Integrateur,Admin")]
        public IActionResult LoadSessions(int structureId, string langCode, int eventId, int identityId)
        {

            return LoadSessionsP(structureId, langCode, eventId, 0, identityId, 0, 0, "", "");
        }


        /// <summary>
        /// Get price grid of the event, for a buyer profil / reseller
        /// </summary>
        /// <param name="structureId">The structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <param name="eventId">The event identifier</param>
        /// <param name="identityId">The customer identifier</param>
        /// <param name="webUserId">The web user identifier</param>        
        /// <param name="bpLogin">Buyer profil / Reseller login</param>
        /// <param name="bpPassword">Buyer profil /Reseller password</param>
        /// <returns></returns>

        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Event))]
        [HttpGet]
        [Route("api/{structureId}/Sessions/{eventId}/{langCode}/{identityId}/{webUserId}/{bpLogin}/{bpPassword}")]
        [Authorize(Roles = "Integrateur,Revendeur,Admin")]
        public IActionResult LoadSessions(int structureId, string langCode, int eventId, int identityId, int webUserId, string bpLogin, string bpPassword)
        {
            return LoadSessionsP(structureId, langCode, eventId, 0, identityId, webUserId, 0, bpLogin, bpPassword);
        }

        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Event))]
        [HttpGet]
        [Route("api/{structureId}/Sessions/{eventId}/{sessionId}/{langCode}/{identityId}/{webUserId}/{bpLogin}/{bpPassword}")]
        [Authorize(Roles = "Integrateur,Revendeur,Admin")]
        public IActionResult LoadSessions(int structureId, string langCode, int eventId, int sessionId, int identityId, int webUserId, string bpLogin, string bpPassword)
        {
            return LoadSessionsP(structureId, langCode, eventId, sessionId, identityId, webUserId, 0, bpLogin, bpPassword);

        }


        /// <summary>
        /// Get price grid of for a buyer profil / reseller
        /// </summary>
        /// <remarks>caching 60s</remarks>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <param name="eventId">The event identifier</param>        
        /// <param name="bpLogin">The Buyer profil / Reseller login</param>
        /// <param name="bpPassword">The Buyer profil / Reseller password</param>
        /// <returns></returns>
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Event))]
        [HttpGet]
        [Route("api/{structureId}/Sessions/{eventId}/{langCode}/{bpLogin}/{bpPassword}")]
        [Authorize(Roles = "Integrateur,Revendeur,Admin")]
        public IActionResult LoadSessions(int structureId, string langCode, int eventId, string bpLogin, string bpPassword)
        {

            return LoadSessionsP(structureId, langCode, eventId, 0, 0, 0, 0, bpLogin, bpPassword);
        }



        /// <summary>
        /// Get the reference currency
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <returns></returns>
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 1800)]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Currency))]        
        [HttpGet]
        [Route("api/{structureId}/Currency")]
        //  [Authorize(Roles = "Revendeur,Integrateur,Admin")]
        public IActionResult GetDeviseRef(int structureId)
        {
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }
            try
            {
                var ret = _deviseManager.GetDeviseRef(structureId);
                Currency curr = _mapper.Map<Currency>(ret);
                Logger.Debug(structureId, $"GetDeviseRef({structureId},{ret}) ok (id={curr})");

                return Ok(curr);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"GetDeviseRef({structureId}) , " +
                         $"{ex.Message} {ex.StackTrace}");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// Load sessions
        /// </summary>
        /// <param name="structureId">The unique structure identifier</param>
        /// <param name="langCode">The Language code set in Rodrigue (fr,de,en,it...). Note **fr** or **de** is set by default</param>
        /// <param name="eventId">The event identifier</param>
        /// <param name="sessionId">The session identifier</param>
        /// <param name="identityId">The customer identifier</param>
        /// <param name="webUserId">The webuser idenifier</param>
        /// <param name="buyerProfilId">The buyer profil identifier</param>
        /// <param name="bpLogin">The buyer profil/Reseller login</param>
        /// <param name="bpPassword">The buyer profil/Reseller password</param>
        /// <returns></returns>
        private IActionResult LoadSessionsP(int structureId, string langCode, int eventId, int sessionId, int identityId, int webUserId, int buyerProfilId, string bpLogin, string bpPassword)
        {
            //[Route("api/{structureId}/Sessions/{eventId}/{langCode}/{identityId}/{webUserId}/{bpLogin}/{bpPassword}")]
            Logger.Debug(structureId, $"LoadSessions({structureId},{langCode},{eventId},{sessionId},{identityId},{webUserId},{buyerProfilId},{bpLogin},{bpPassword})...");

            var accT = Request.Headers[HeaderNames.Authorization];

            if (!TokenManager.PartnerHasRight(structureId, accT))
            {
                //Exception ex = new UnauthorizedAccessException ($"structureId {structureId} is not allowed");
                var pb = Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);
                return pb;
            }

            var listRet = _priceManager.LoadPricesGrid(structureId, langCode, eventId, sessionId, identityId, webUserId, buyerProfilId, bpLogin, bpPassword, 0);

            List<Event> lev = (List<Event>)_mapper.Map(listRet, typeof(List<EventDTO>), typeof(List<Event>));

            if (lev != null && lev.Count == 1)
            {
                Logger.Debug(structureId, $"LoadSessions({structureId},{langCode},{eventId},{sessionId},{identityId},{webUserId},{buyerProfilId},{bpLogin},{bpPassword}) ok");
                return Ok(lev[0]);
            }
            else
            {
                if (lev == null)
                {
                    Logger.Error(structureId, $"LoadSessions return null");
                    return Problem($"response for event {eventId} return null ?!", statusCode: StatusCodes.Status406NotAcceptable);
                }
                else
                {
                    Logger.Error(structureId, $"LoadSessions {eventId} response return {lev.Count} elements ?!");
                    return Problem($"Sessions for event {eventId} return {lev.Count} elements ?!", statusCode: StatusCodes.Status406NotAcceptable);
                }
            }
        }
    }
}
