﻿using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Enums;
using Core.Themis.Libraries.DTO.exposedObjects;
using Core.Themis.Libraries.DTO.Orders;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.DTO.Transactionnals;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;

namespace Core.Themis.API.Offers.Controllers
{

    [ApiController]
    public class OrderController : ControllerBase
    {
        private readonly IConfiguration _configuration;

        private readonly IRodrigueConfigIniDictionnary _rodrigueConfigIniDictionnary;
        private readonly IBasketManager _basketManager;
        private readonly IWebUserManager _webuserManager;
        private readonly IFiliereManager _filiereManager;
        private readonly IOperatorManager _operatorManager;
        private readonly IOrderManager _orderManager;
        private readonly IBuyerProfilManager _buyerProfilManager;
        private readonly ISeatManager _seatManager;

        private static readonly RodrigueNLogger Logger = new();

        public OrderController(
            IConfiguration configuration,
            IBasketManager basketManager, 
            IWebUserManager webuserManager, 
            IOrderManager orderManager, 
            IFiliereManager filiereManager,
            IOperatorManager opeatorManager, 
            IBuyerProfilManager buyerProfilManager,
            ISeatManager seatManager,
            IRodrigueConfigIniDictionnary rodrigueConfigIniDictionnary)
        {
            _configuration = configuration;
            _rodrigueConfigIniDictionnary = rodrigueConfigIniDictionnary;
            _basketManager = basketManager;
            _webuserManager = webuserManager;
            _orderManager = orderManager;
            _filiereManager = filiereManager;
            _operatorManager = opeatorManager;
            _buyerProfilManager = buyerProfilManager;
            _seatManager = seatManager;
        }


        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<BasketDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [HttpGet]
        //[Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/Order/{orderId}/{basketId}/{langCode}")]
        public IActionResult GetOrder(int structureId, int orderId, int basketId, string langCode)
        {
            Logger.Debug(structureId, $"GetOrder({structureId},{basketId},{orderId})...");

            try
            {
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                if (basketId != 0)
                {
                    List<BasketDTO> listB = _basketManager.GetAllBasketInfo(structureId, basketId, 0);

                    if (listB.Count != 1)
                        return Problem($"can't retrieve basket {basketId}", null, StatusCodes.Status404NotFound);

                    BasketDTO bask = listB[0];

                    if (orderId > 0 && bask.OrderId != orderId)
                        return Problem($"can't retrieve basket {basketId} with order {orderId}", null, StatusCodes.Status409Conflict);
                    else
                        orderId = bask.OrderId;
                }

                if (orderId == 0)
                    return Problem($"can't retrieve order 0", null, StatusCodes.Status409Conflict);

                string configIniPath = _configuration["ConfigIniPath"].ToString();
                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);

                int operatorId = 0;
                operatorId = OperatorManager.GetOperateurPaiement(configIniXml);

                OrderDTO order = _orderManager.Load(structureId, langCode, orderId, 0);

                return Ok(order);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"EditionOrder({structureId}, {basketId}) {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message);
            }
        }


        /// <summary>
        /// création de la commande + paiement de cette commande
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="basketId"></param>
        /// <param name="langCode"></param>
        /// <param name="amount">montant payé, en cents</param>
        /// <param name="customerName">sera inseré dans dossier_client_nom</param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(OrderDTO))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]        
        [HttpPost]
        [Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/Order/{basketId}/{amount}")]
        [Route("api/{structureId}/Order/{basketId}/{amount}/{langCode}")]
        public IActionResult CreateOrder(int structureId, int basketId, int amount, 
             string langCode, [FromBody] CreateOrderDemand orderDemand
            )
        {
            Logger.Debug(structureId, $"CreateOrder({structureId},{basketId},{amount})...");

            if (langCode == null)
                langCode = "en";

            try
            {
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                List<BasketDTO> listB = _basketManager.GetAllBasketInfo(structureId, basketId, 0);

                if (listB.Count != 1)
                    return Problem($"can't retrieve basket {basketId}", null, StatusCodes.Status404NotFound);

                BasketDTO bask = listB[0];

                switch (bask.Etat)
                {
                    case "P":
                    case "V":
                        return Problem($"basket state is already in state {bask.Etat}", null, StatusCodes.Status406NotAcceptable);
                    default:
                        break;
                }

                if (bask.IdentityId == 0 && bask.WebUser.UserId > 0)
                {
                    Libraries.DTO.WTObjects.WebUser webUserRnt = _webuserManager.Get(structureId, bask.WebUser.UserId);
                    bask.IdentityId = webUserRnt.IdentiteId;
                    _basketManager.UpdateIdentiteId(structureId, bask.BasketId, webUserRnt.IdentiteId);
                }

                _basketManager.UpdateEtatPanier(structureId, bask.BasketId, "P");
                // bloque les places

                _basketManager.SetEtapeCreateCommande(structureId, bask, "REFLAGPLACES", EtapeCreationCmd.PrisEnCompte, "api offer");

                List<SeatDTO> listSeats = bask.ListAllSeats();

                List<SeatDTO> listSeatsUpdated = _seatManager.FlagAndLockSeats(structureId, listSeats, bask.UserId.ToString());
                if (listSeats.Count != listSeatsUpdated.Count)
                {
                    _basketManager.SetEtapeCreateCommande(structureId, bask, "REFLAGPLACES", EtapeCreationCmd.Echoue, "reflag unsucceeded");

                    Logger.Error(structureId, $"CreateOrder({structureId},{basketId},{amount}) can't reflag");
                    return Problem("can't reflag");
                }
                else
                {
                    Logger.Debug(structureId, $"CreateOrder({structureId},{basketId},{amount}) reflag ok");

                    _basketManager.SetEtapeCreateCommande(structureId, bask, "REFLAGPLACES", EtapeCreationCmd.Termine, "reflag succeeded");
                    _basketManager.SetEtapeCreateCommande(structureId, bask, "CREATIONCMD", EtapeCreationCmd.PrisEnCompte, "api offer");

                string configIniPath = _configuration["ConfigIniPath"].ToString();
                var configIniXml = _rodrigueConfigIniDictionnary.GetDictionaryFromCache(structureId);

                    int posteId = 0;
                    if (configIniXml.ContainsKey("PAIEMENTWEBPOSTID") && configIniXml["PAIEMENTWEBPOSTID"] != null && configIniXml["PAIEMENTWEBPOSTID"] != "")
                    {
                        posteId = int.Parse(configIniXml["PAIEMENTWEBPOSTID"].ToString());
                    }
                    int idFiliereCreatProfilAnony = FiliereManager.GetFiliereCreationProfil(configIniXml);

                    int idFiliereVU = FiliereManager.GetFiliereVU(configIniXml);
                    int idFiliereAbo = FiliereManager.GetFiliereABO(configIniXml);
                    int idFiliereReabo = FiliereManager.GetFiliereReABO(configIniXml);
                    int idFiliereProduits = FiliereManager.GetFiliereProduits(configIniXml);
                    int operatorId = 0;
                    operatorId = OperatorManager.GetOperateurPaiement(configIniXml);

                    BuyerProfilDTO bp = new();

                    /************** creation commande ********/

                    string dossierClientNom = "";

                    if (orderDemand != null && orderDemand.Consumer != null)
                    {
                        dossierClientNom = ((string.IsNullOrEmpty(orderDemand.Consumer.CustomerLastName) ? "" : orderDemand.Consumer.CustomerLastName) + " " +
                            (string.IsNullOrEmpty(orderDemand.Consumer.CustomerLastName) ? "" : orderDemand.Consumer.CustomerFirstName) + " " +
                            (string.IsNullOrEmpty(orderDemand.Consumer.Email) ? "" : orderDemand.Consumer.Email)).Trim();
                        if (dossierClientNom.Length > 50)
                        {
                            dossierClientNom = dossierClientNom.Substring(0, 50);
                        }
                    }

                    List<PaymentMethodDTO> listPM = new();
                    if (bask.WebUser.ProfilAcheteurId != 0)
                    {
                        bp = _buyerProfilManager.GetBuyerProfilById(structureId, bask.WebUser.ProfilAcheteurId);
                        operatorId = bp.OperatorPaymentId;
                        if (bp.IsReseller && bp.ListPaymentMethods != null && bp.ListPaymentMethods.Count == 1)
                        {
                            listPM.Add(bp.ListPaymentMethods[0]);
                        }
                    }
                    if (listPM.Count == 1)
                    {
                        listPM[0].Montant = amount;
                    }


                    OrderDTO ord = _orderManager.CreateOrderFromPanier(structureId, bask, operatorId, idFiliereCreatProfilAnony, idFiliereAbo, idFiliereVU, idFiliereReabo, idFiliereProduits,
                         false, bask.IdentityId, dossierClientNom);

                    int orderAmount = 0;
                    foreach (var p in ord.ListDossiersSeats)
                    {
                        orderAmount += p.Amount;
                    }
                    foreach (var p in ord.ListDossiersProduct)
                    {
                        orderAmount += p.TotalTTCAmount;
                    }
                    if (amount == 0)
                        amount = orderAmount;



                    if (ord.OrderId > 0)
                    {
                        Logger.Debug(structureId, $"CreateOrder({structureId},{basketId},{amount}) create order ok ({ord.OrderId})");

                        _basketManager.UpdateOrderId(structureId, bask.BasketId, ord.OrderId);
                        bask.OrderId = ord.OrderId;
                        _basketManager.SetEtapeCreateCommande(structureId, bask, "CREATIONCMD", EtapeCreationCmd.Termine, "api offer");

                        /*************** payment commande */
                        _basketManager.SetEtapeCreateCommande(structureId, bask, "CREATIONPAIEM", EtapeCreationCmd.PrisEnCompte, "api offer");

                        try
                        {
                            string myReferenceTransaction = "";
                            if (orderDemand.TransactionReference != null && !string.IsNullOrEmpty(orderDemand.TransactionReference))
                            {
                                myReferenceTransaction = orderDemand.TransactionReference;

                            }
                            ord = _orderManager.Pay(structureId, bask, operatorId, idFiliereVU, bask.IdentityId, ord, listPM, null, myReferenceTransaction, Environment.MachineName, posteId);

                            if (!string.IsNullOrEmpty(ord.MsgErreur))
                            {
                                _basketManager.SetEtapeCreateCommande(structureId, bask, "CREATIONPAIEM", EtapeCreationCmd.Echoue, "api offer");
                                return Problem(ord.MsgErreur);
                            }
                        }
                        catch
                        {
                            _basketManager.SetEtapeCreateCommande(structureId, bask, "CREATIONPAIEM", EtapeCreationCmd.Echoue, "api offer");
                            throw;
                        }
                        Logger.Debug(structureId, $"CreateOrder({structureId},{basketId},{amount}) pay order ok ({ord.OrderId})");

                        _basketManager.SetEtapeCreateCommande(structureId, bask, "CREATIONPAIEM", EtapeCreationCmd.Termine, "api offer");


                        _basketManager.SetEtapeCreateCommande(structureId, bask, "EDITION", EtapeCreationCmd.PrisEnCompte, "api offer");

                        _orderManager.DoEdition(structureId, operatorId, ord);

                        Logger.Debug(structureId, $"CreateOrder({structureId},{basketId},{amount}) edition order ok ({ord.OrderId})");

                        _basketManager.SetEtapeCreateCommande(structureId, bask, "EDITION", EtapeCreationCmd.Termine, "api offer");

                        _basketManager.UpdateEtatPanier(structureId, bask.BasketId, new List<string> { "P" }, "V");
                    }
                    else
                    {
                        Logger.Error(structureId, $"CreateOrder({structureId},{basketId},{amount}) create order ko");
                        _basketManager.SetEtapeCreateCommande(structureId, bask, "CREATIONCMD", EtapeCreationCmd.Echoue, "api offer");
                    }

                    OrderDTO order = _orderManager.Load(structureId, langCode, ord.OrderId, 0);

                    return Ok(order);
                }
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"CreateOrder({structureId}, {basketId}) {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message);
            }
        }
    }
}
