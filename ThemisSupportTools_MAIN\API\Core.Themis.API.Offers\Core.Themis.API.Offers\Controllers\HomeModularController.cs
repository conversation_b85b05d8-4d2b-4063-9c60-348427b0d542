﻿using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System;
using System.Linq;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.EventsSessions.Interfaces;
using Core.Themis.Libraries.BLL.Managers.HomeModular.Interfaces;
using System.Collections.Generic;
using Core.Themis.Libraries.BLL.Products.Interfaces;

namespace Core.Themis.API.Offers.Controllers
{
    [ApiController]
    public class HomeModularController : ControllerBase
    {
        private readonly IConfiguration _configuration;

        public readonly IProductManager _productManager;
        public readonly IEventManager _eventManager;
        public readonly IEventGenreManager _eventGenreManager;
        public readonly IHomeModularManager _homeModularManager;

        private static readonly RodrigueNLogger Logger = new();

  
        public HomeModularController(
            IConfiguration configuration,
            IProductManager productManager, 
            IEventManager eventManager,
            IEventGenreManager eventGenreManager,
            IHomeModularManager homeModularManager)
        {
            _configuration = configuration;
            _productManager = productManager;
            _eventManager = eventManager;
            _eventGenreManager = eventGenreManager;
            _homeModularManager = homeModularManager;
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<EventDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpGet]
        //[Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/homeModular/blockUserConfig/{langCode}/{emplacementGroupId}")]
        public IActionResult HomeModularBlockUserConfig(int structureId, string langCode, int emplacementGroupId = 1)
        {

            Logger.Debug(structureId, $"HomeModularBlockUserConfig({structureId})...");
      
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                var result = _homeModularManager.GetHomeModularEmplacementGroupById(structureId, emplacementGroupId, langCode);
                return Ok(result);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"HomeModularBlockUserConfig({structureId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }


        /// <summary>
        /// Check si les tables Home Modular existent 
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(bool))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpGet]
        //[Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/homeModular/checkAccessHome")]
        public IActionResult CheckAccessHomeModular(int structureId)
        {
            Logger.Debug(structureId, $"CheckAccessHomeModular({structureId})...");

            var accT = Request.Headers[HeaderNames.Authorization];

            Logger.Trace(structureId, $"token: {accT}");

            //if (!TokenManager.PartnerHasRight(structureId, accT))
              //  return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
                var result = _homeModularManager.CheckHomeModularBlockUserConfig(structureId);

                return Ok(result);
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"HomeModularBlockUserConfig({structureId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }



        /// <summary>
        /// Charge une liste de manifestations pour un emplacementId (Liste de manifestation en mode carousel)
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="langCode"></param>
        /// <param name="isHomeTeasing"></param>
        /// <returns>Retourne un dictionnary ave en clé l'emplacement Id et en valeur la liste des manifestations associée</returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Dictionary<int, List<EventDTO>>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpGet]
        //[Authorize(Roles = "User,Viewer")]
        [Route("api/{structureId}/homeModular/loadEventsFeatures/{langCode}/{isHomeTeasing}")]
        public IActionResult LoadEventsFeatures(int structureId, string langCode, bool isHomeTeasing)
        {
            Logger.Debug(structureId, $"LoadEventsFeatures({structureId} {langCode} {isHomeTeasing})...");
            
            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            try
            {
         
                var emplacementGroups = _homeModularManager.GetAllEmplacementGroups(structureId);
                var homeModularUserConfigs = _homeModularManager.GetHomeModularEmplacementGroupById(structureId, emplacementGroups.FirstOrDefault().Id, langCode, isHomeTeasing);


                var result = _eventManager.GetEventsFeatures(structureId, langCode, homeModularUserConfigs, 0, isHomeTeasing);
                return Ok(result);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, ex, $"HomeModularBlockUserConfig({structureId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
    }
}
