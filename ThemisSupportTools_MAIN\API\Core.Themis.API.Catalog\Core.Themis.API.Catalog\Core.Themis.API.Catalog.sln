﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.33627.172
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.API.Catalog", "Core.Themis.API.Catalog\Core.Themis.API.Catalog.csproj", "{22B37874-713F-469C-AA45-6F60E6BC2948}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLL", "..\..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL.csproj", "{D8E7E457-D414-4F07-B8A6-CACE17D2C472}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLLTests", "..\..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLLTests\Core.Themis.Libraries.BLLTests.csproj", "{7A04A0A3-9C10-44E8-921B-44CDB07C6BC0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Data", "..\..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Core.Themis.Libraries.Data.csproj", "{A43D94F8-026E-4252-9DE9-4223C24B20E4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DataTests", "..\..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Libraries.DataTests\Core.Themis.Libraries.DataTests.csproj", "{E233A5FF-2836-4825-A3CA-DBB9606BF7D3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DTO", "..\..\..\Libraries\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj", "{67D92B20-F25D-4BD4-9FD1-41BB5AE5DE96}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Utilities", "..\..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj", "{7FB69756-59CC-4F2E-BAE3-A9828DA7F9FB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.UtilitiesTests", "..\..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.UtilitiesTests\Core.Themis.Libraries.UtilitiesTests.csproj", "{4B259994-4EE7-4AF4-93BD-55B9E5AA3786}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{22B37874-713F-469C-AA45-6F60E6BC2948}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{22B37874-713F-469C-AA45-6F60E6BC2948}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{22B37874-713F-469C-AA45-6F60E6BC2948}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{22B37874-713F-469C-AA45-6F60E6BC2948}.Release|Any CPU.Build.0 = Release|Any CPU
		{D8E7E457-D414-4F07-B8A6-CACE17D2C472}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D8E7E457-D414-4F07-B8A6-CACE17D2C472}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D8E7E457-D414-4F07-B8A6-CACE17D2C472}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D8E7E457-D414-4F07-B8A6-CACE17D2C472}.Release|Any CPU.Build.0 = Release|Any CPU
		{7A04A0A3-9C10-44E8-921B-44CDB07C6BC0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7A04A0A3-9C10-44E8-921B-44CDB07C6BC0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7A04A0A3-9C10-44E8-921B-44CDB07C6BC0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7A04A0A3-9C10-44E8-921B-44CDB07C6BC0}.Release|Any CPU.Build.0 = Release|Any CPU
		{A43D94F8-026E-4252-9DE9-4223C24B20E4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A43D94F8-026E-4252-9DE9-4223C24B20E4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A43D94F8-026E-4252-9DE9-4223C24B20E4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A43D94F8-026E-4252-9DE9-4223C24B20E4}.Release|Any CPU.Build.0 = Release|Any CPU
		{E233A5FF-2836-4825-A3CA-DBB9606BF7D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E233A5FF-2836-4825-A3CA-DBB9606BF7D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E233A5FF-2836-4825-A3CA-DBB9606BF7D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E233A5FF-2836-4825-A3CA-DBB9606BF7D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{67D92B20-F25D-4BD4-9FD1-41BB5AE5DE96}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67D92B20-F25D-4BD4-9FD1-41BB5AE5DE96}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67D92B20-F25D-4BD4-9FD1-41BB5AE5DE96}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67D92B20-F25D-4BD4-9FD1-41BB5AE5DE96}.Release|Any CPU.Build.0 = Release|Any CPU
		{7FB69756-59CC-4F2E-BAE3-A9828DA7F9FB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7FB69756-59CC-4F2E-BAE3-A9828DA7F9FB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7FB69756-59CC-4F2E-BAE3-A9828DA7F9FB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7FB69756-59CC-4F2E-BAE3-A9828DA7F9FB}.Release|Any CPU.Build.0 = Release|Any CPU
		{4B259994-4EE7-4AF4-93BD-55B9E5AA3786}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4B259994-4EE7-4AF4-93BD-55B9E5AA3786}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4B259994-4EE7-4AF4-93BD-55B9E5AA3786}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4B259994-4EE7-4AF4-93BD-55B9E5AA3786}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C3622707-304C-46F0-A5FF-A8A80AC19159}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 9
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://v-dev-test:8080/tfs/appscollection
		SccProjectUniqueName0 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj
		SccProjectName0 = ../../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLL
		SccLocalPath0 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL
		SccProjectUniqueName1 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests\\Core.Themis.Libraries.BLLTests.csproj
		SccProjectName1 = ../../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLLTests
		SccLocalPath1 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests
		SccProjectUniqueName2 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj
		SccProjectName2 = ../../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Librairies.Data
		SccLocalPath2 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data
		SccProjectUniqueName3 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests\\Core.Themis.Libraries.DataTests.csproj
		SccProjectName3 = ../../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Libraries.DataTests
		SccLocalPath3 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests
		SccProjectUniqueName4 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj
		SccProjectName4 = ../../../Libraries/Core.Themis.Libraries.DTO
		SccLocalPath4 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.DTO
		SccProjectUniqueName5 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj
		SccProjectName5 = ../../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.Utilities
		SccLocalPath5 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities
		SccProjectUniqueName6 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests\\Core.Themis.Libraries.UtilitiesTests.csproj
		SccProjectName6 = ../../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.UtilitiesTests
		SccLocalPath6 = ..\\..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests
		SccLocalPath7 = .
		SccProjectUniqueName8 = Core.Themis.API.Catalog\\Core.Themis.API.Catalog.csproj
		SccProjectName8 = Core.Themis.API.Catalog
		SccLocalPath8 = Core.Themis.API.Catalog
	EndGlobalSection
EndGlobal
