﻿using Core.Themis.Libraries.BLL.Managers.Order.OpinionOrder.Interfaces;
using Core.Themis.Libraries.DTO.Opinions;
using Core.Themis.Libraries.DTO.Opinions.OpinionOrder;
using Core.Themis.Libraries.Utilities.API;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.CustomErrors;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using System;
using System.Net;

namespace Core.Themis.API.Customers.Controllers
{

    [Authorize(Roles = "Admin, Viewer, User")]
    //[Route("api/[controller]")]
    [ApiController]
    public class OpinionOrderController : ControllerBase
    {

        private readonly IHttpContextAccessor _httpContext;
        private readonly IOpinionOrderManager _opinionOrderManager;

        private static readonly RodrigueNLogger Logger = new();

        public OpinionOrderController(
            IHttpContextAccessor httpContext,
            IOpinionOrderManager opinionOrderManager)
        {
            _httpContext = httpContext;
            _opinionOrderManager = opinionOrderManager;
        }


        /// <summary>
        /// Charge les avis 
        /// </summary>
        /// <param name="structureId">structure id du client</param>
        /// <param name="orderId">Id de la Commande</param>
        /// <param name="basketId">Id du panier</param>
        /// <returns>retourne un formulaire avec les questions ou un formulaire avec les réponses</returns>
        [HttpGet]
        [Route("api/{structureId}/opinionOrder/{orderid}/{basketid}")]
        public IActionResult LoadOpinionOrder(int structureId, int orderId, int basketId)
        {
            Logger.Trace(structureId, "LoadOpinionOrder");

            try
            {
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                string theNameFromToken = _httpContext.HttpContext.User.Identity.Name;

                return Ok(_opinionOrderManager.LoadOpinionOrderQuestionList(structureId, orderId, basketId));
            }
            catch (OrderIdRodrigueNotFoundException ex)
            {
                Logger.Error(structureId, $"OrderIdRodrigueNotFoundException Message = {ex.Message} - statTrace = {ex.StackTrace}");

                return Problem(ex.Message, null, (int)ex.ErrorCode);
            }
            catch (Exception ex)
            {
                Logger.Error(structureId, $"Message = {ex.Message} - stackTrace = {ex.StackTrace}");

                return Problem(ex.Message, null);
            }
        }

        //[ProducesResponseType(StatusCodes.Status200OK, Type = typeof(HttpResponseMessage))]
        //[ProducesResponseType(StatusCodes.Status400BadRequest)]
        //[ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        /// <summary>
        /// Ajoute les réponses pour un avis
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="orderId"></param>
        /// <param name="opinionOrderResponseForm"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("api/{structureId}/opinionOrder/{orderId}")]
        public IActionResult CreateOpinionOrderResponse(int structureId, int orderId, [FromBody] OpinionOrderResponseFormDTO opinionOrderResponseForm)
        {
            Logger.Trace(structureId, "---- start CreateOpinionOrderResponse ----");

            var accT = Request.Headers[HeaderNames.Authorization];
            if (!TokenManager.PartnerHasRight(structureId, accT))
                return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

            CustomResponse custResp = new()
            {
                HttpStatusCode = HttpStatusCode.Ambiguous,
                Object = opinionOrderResponseForm
            };

            if (orderId == opinionOrderResponseForm.OrderId)
            {
                try
                {
                    int opinionOrderCreated = _opinionOrderManager.CreateOpinionOrderResponse(structureId, opinionOrderResponseForm);

                    if (opinionOrderCreated > 0)
                    {
                        custResp = new CustomResponse
                        {
                            HttpStatusCode = HttpStatusCode.Created,
                            Message = ""
                        };
                    }
                }
                catch (OrderIdExistException ex)
                {
                    Logger.Error(structureId, "CreateOpinionOrderResponse:" + ex.Message + " " + ex.StackTrace);

                    custResp = new CustomResponse
                    {
                        HttpStatusCode = ex.ErrorCode,
                        Message = ex.Message
                    };

                    // throw ex;
                }
                catch (Exception ex)
                {
                    Logger.Error(structureId, "CreateOpinionOrderResponse:" + ex.Message + " " + ex.StackTrace);

                    custResp = new CustomResponse
                    {
                        HttpStatusCode = HttpStatusCode.BadRequest,
                        Message = ex.Message
                    };
                    // throw ex;
                }
                return Ok(custResp);
            }

            return Problem();
        }


        /// <summary>
        /// Liste les avis par pagination
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="lstSaleChannels"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="displayMode">pagination</param>
        /// <param name="itemsPerPage">5</param>
        /// <param name="page">0</param>
        /// <param name="orderId">0</param>
        /// <returns></returns>
        [HttpGet]
        [Route("api/{structureId}/opinionOrder/list")]
        public IActionResult OpinionOrderList(int structureId, string lstSaleChannels, string startDate, string endDate, string displayMode = "pagination", int itemsPerPage = 5, int page = 0, int orderId = 0)
        {
            try
            {
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                string theNameFromToken = _httpContext.HttpContext.User.Identity.Name;

                OpinionOrderResponse responses = _opinionOrderManager.LoadOpinionOrderList(structureId, lstSaleChannels, startDate, endDate, displayMode, itemsPerPage, page, orderId);
                return Ok(responses);
            }
            catch (OrderIdRodrigueNotFoundException ex)
            {
                Logger.Error(structureId, $"OpinionOrderList : {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message);
            }
        }

        [HttpGet]
        [Route("api/{structureId}/opinionOrder/average/{questionCode}")]
        public IActionResult LoadOpinionOrderAverage(int structureId, string questionCode, string lstSaleChannels, string startDate, string endDate)
        {
            try
            {
                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                string theNameFromToken = _httpContext.HttpContext.User.Identity.Name;

                OpinionAverageDTO responses = _opinionOrderManager.LoadOpinionAverageList(structureId, questionCode, lstSaleChannels, startDate, endDate);
                return Ok(responses);
            }
            catch (OrderIdRodrigueNotFoundException ex)
            {
                Logger.Error(structureId, $"LoadOpinionOrderAverage : {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message);
            }
        }

        [HttpGet]
        [Route("api/{structureId}/opinionOrder/averagegraph/{questionCode}")]
        public IActionResult LoadOpinionOrderAverageGraph(int structureId, string questionCode, string lstSaleChannels, string startDate, string endDate)
        {
            try
            {
                string theNameFromToken = _httpContext.HttpContext.User.Identity.Name;

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                OpinionAverageDTO responses = _opinionOrderManager.LoadOpinionAverageGraph(structureId, questionCode, lstSaleChannels, startDate, endDate);
                return Ok(responses);
            }
            catch (OrderIdRodrigueNotFoundException ex)
            {
                Logger.Error(structureId, $"LoadOpinionOrderAverageGraph : {ex.Message} {ex.StackTrace}");
                return Problem(ex.Message);
            }
        }
    }
}
