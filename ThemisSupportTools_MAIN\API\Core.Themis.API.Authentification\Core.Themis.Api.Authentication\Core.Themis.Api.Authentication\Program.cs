using AspNetCoreRateLimit;
using Core.Themis.Libraries.Utilities.Helpers;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.Filters;
using Core.Themis.Libraries.BLL.Extentions.ServicesBuilder;
//using Core.Themis.Libraries.Utilities.Helpers;
using System.Reflection;
using Microsoft.AspNetCore.Authorization;
using Core.Themis.Api.Authentication.Handlers;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Diagnostics;
using System.Text;
using Azure;
using Core.Themis.Libraries.BLL.Services.RedisCache.Interfaces;
using Core.Themis.Libraries.BLL.Services.RedisCache;
using Microsoft.Extensions.Caching.Distributed;
using StackExchange.Redis;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.


builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(
        policy =>
        {

            policy.WithOrigins("https://dev.themisweb.fr", "https://externedemo.rodrigue-solutions.com"
                ).AllowAnyHeader();
        });
});


builder.Services.AddControllers();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();


builder.Services.AddScoped<IAuthorizationHandler, RolesAuthorizationHandler>();



// Rodrigue injection
builder.Services.AddRodrigueDataServices();
builder.Services.AddRodrigueManager();
builder.Services.AddRodrigueMapper();


builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();


#region Redis cache
string instanceName = "Rodrigue_cache_" + Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") + ".";
builder.Services.AddRodrigueRedis(builder.Configuration.GetConnectionString("Redis")!, instanceName);

builder.Services.AddSingleton<IRedisCacheService>(sp =>
    new RedisCacheService(
        instanceName,
        sp.GetRequiredService<IDistributedCache>(),
        sp.GetRequiredService<IConnectionMultiplexer>()
    )
);
#endregion

builder.Services.Configure<IpRateLimitOptions>(options =>
{
    options.EnableEndpointRateLimiting = true;
    options.StackBlockedRequests = false;
    options.HttpStatusCode = 429;
    options.RealIpHeader = "X-Real-IP";
    options.ClientIdHeader = "X-ClientId";



    options.GeneralRules = new List<RateLimitRule>
        {
            new RateLimitRule
            {
                Endpoint = "*",
                Period = "1s",
                Limit = 20,
            }
        };
});

builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters()
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ClockSkew = Debugger.IsAttached ? TimeSpan.Zero : TimeSpan.FromMinutes(10),
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = "rodrigue",
        ValidAudience = "ThemisAPI",
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["ApiSecretKey"]!))
    };
});





ConfigurationHelper.Initialize(builder.Configuration);

builder.Services.AddResponseCaching();

builder.Services.AddSingleton<IIpPolicyStore, MemoryCacheIpPolicyStore>();
builder.Services.AddSingleton<IRateLimitCounterStore, MemoryCacheRateLimitCounterStore>();
builder.Services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
builder.Services.AddSingleton<IProcessingStrategy, AsyncKeyLockProcessingStrategy>();
builder.Services.AddInMemoryRateLimiting();

// In-Memory Caching
builder.Services.AddMemoryCache();



//builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

builder.Services.AddSwaggerGen(opts => opts.EnableAnnotations());

builder.Services.AddRazorPages();


builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc("v1", new OpenApiInfo
    {
        Version = "v1",
        Title = "Themis Authentication API",
        Description = "An API to get tokens",
        //TermsOfService = new Uri("https://example.com/terms"),
        //Contact = new OpenApiContact
        //{
        //    Name = "Example Contact",
        //    Url = new Uri("https://example.com/contact")
        //},
        //License = new OpenApiLicense
        //{
        //    Name = "Example License",
        //    Url = new Uri("https://example.com/license")
        //}
    });
    var xmlFilename = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, xmlFilename));

    options.UseAllOfToExtendReferenceSchemas();

    //options.SupportNonNullableReferenceTypes();

    //Assert.False(schema.Properties["StringWithRequiredDisallowNull"].Nullable);

    List<string> xmlFiles = Directory.GetFiles(AppContext.BaseDirectory, "*.xml", SearchOption.TopDirectoryOnly).ToList();
    xmlFiles.ForEach(xmlFile => options.IncludeXmlComments(xmlFile));


    options.ExampleFilters();

    options.OperationFilter<SecurityRequirementsOperationFilter>(true, "Bearer");
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "Standard Authorization header using the Bearer scheme (JWT). Example: \"bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });


});

builder.Services.AddSwaggerExamplesFromAssemblies(Assembly.GetEntryAssembly());


builder.Logging.ClearProviders();
builder.Logging.SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Trace);
NLog.LogManager.Configuration.Variables.Add("environment", Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"));

string vers = Directory.GetParent(AppContext.BaseDirectory).Name;
NLog.LogManager.Configuration.Variables.Add("version", vers);

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}




    app.UseSwagger();
    app.UseSwaggerUI();


app.UseHttpsRedirection();

app.UseIpRateLimiting();

app.UseResponseCaching();

app.UseCors();

//Access to XMLHttpRequest at 'https://localhost:7022/api/994/token/WEBSITEEXTERNE' from origin 'https://dev.themisweb.fr' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.


app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();


app.Run();
