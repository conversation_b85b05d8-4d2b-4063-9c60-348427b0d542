﻿<UserControl x:Class="ModulePlatformsTranslations.Views.PlatformsTranslations"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ModulePlatformsTranslations.Views"
             xmlns:prism="http://prismlibrary.com/"
             prism:ViewModelLocator.AutoWireViewModel="True"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!--Menu-->
        <StackPanel Orientation="Horizontal" Grid.Row="0" HorizontalAlignment="Stretch" VerticalAlignment="Stretch"   >

            <Button Content="Site Indiv" Command="{Binding UpdateViewCommand}" Style="{StaticResource MaterialDesignOutlinedDarkButton}" CommandParameter="Indiv" Margin="10" Width="200" />
            <Button Content="Customer" Command="{Binding UpdateViewCommand}"  Style="{StaticResource MaterialDesignOutlinedDarkButton}" CommandParameter="Customer" Margin="10" Width="200"/>
        </StackPanel>

        <StackPanel Grid.Row="1">

            <ContentControl prism:RegionManager.RegionName="PlatformsRegion"/>

            <!--<ContentControl Content="{Binding SelectedViewModel}"/>-->
        </StackPanel>
    </Grid>
</UserControl>
