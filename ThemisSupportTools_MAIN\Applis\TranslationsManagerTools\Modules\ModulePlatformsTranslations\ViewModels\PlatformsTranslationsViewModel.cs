﻿using Prism.Commands;
using Prism.Mvvm;
using Prism.Regions;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace ModulePlatformsTranslations.ViewModels
{
    public class PlatformsTranslationsViewModel :  BindableBase, INavigationAware
    {

        private readonly IRegionManager _regionManager;

        #region Commands
        public DelegateCommand<string> UpdateViewCommand { get; private set; }

        #endregion

        public PlatformsTranslationsViewModel(IRegionManager regionManager)
        {
            _regionManager = regionManager;
            UpdateViewCommand = new DelegateCommand<string>(ExecutUpdateViewCommand);
        }

        private void ExecutUpdateViewCommand(string viewName)
        {
            if (!string.IsNullOrEmpty(viewName))
                _regionManager.RequestNavigate("PlatformsRegion", viewName);

        }
        public void OnNavigatedTo(NavigationContext navigationContext)
        {
            _regionManager.RequestNavigate("PlatformsRegion", "Indiv");
        }

        public bool IsNavigationTarget(NavigationContext navigationContext)
        {
            return true;
        }

        public void OnNavigatedFrom(NavigationContext navigationContext)
        {
        }
    }
}
