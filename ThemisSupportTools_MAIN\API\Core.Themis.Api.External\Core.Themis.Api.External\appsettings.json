{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },

  "extensions": [
    { "assembly": "NLog.Extensions.Logging" },
    { "assembly": "NLog.Web.AspNetCore" }
  ],


  "AllowedHosts": "*",
  "ApiSecretKey": "secret401b09eab3c013d4ca54922bb802bec8fd5318192b0a75f201d8b3727429090fb337591abd3e44453b954555b7a0812e1081c39b740293f765eae731f5a65ed1",


  "ApiAuthenticationUrl": "http://back-themis-ws/WS/API/AUTHENTICATIONS/v110/api/",

  "ApiCustomerUrl": "https://localhost:5001/api/",
  "ApiOfferUrl": "https://localhost:44329/api/",


  "ApiCatalogUrl": "https://localhost:5001/api/",

  "ApiBrevoUrl": "https://api.brevo.com/v3/",
  "BrevoIpRanges": "************-**************;*************-***************;***********-*************",

  "ActivetrailWebHoookKey": "ACTTRAILK",

  "ConnectionStrings": {
    "WsAdminDB": "Server=************;Database=WSAdmin;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true",
    "GlobalOpinionDB": "Server=************;Database=GlobalOpinions;Persist Security Info=True;User ID=sa;Password=************************;MultipleActiveResultSets=true",
    "WebLibraryDB": "Server=************;Database=GlobalWebLibrary;Persist Security Info=True;User ID=sa;Password=************************;MultipleActiveResultSets=true",
    "GlobalOpinionDBold": "Server=************;Database=GlobalOpinions;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true",
    "WebLibraryDBold": "Server=************;Database=GlobalWebLibrary;Persist Security Info=True;User ID=SphereWeb;Password=************************************************************************************************************;MultipleActiveResultSets=true",
    "Redis": "**************:6555,password=tS4pDJfVCN3Y0j5pvrdEBCpH0gXLZsZ/QCdN2zh6lJwoi+Va4eBUwtkZEUNYCcCl"
  },

  "nlog.config": "D:\\WORK\\Features\\exports_Vardar\\API\\Core.Themis.Api.External\\Core.Themis.Api.External\\nlog.config",

  "PathForSqlScript": "LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql",
  "configFileTransfers": "configTransfers.json",

  "WsAdminConnectionCache": 300,
  "TypeRun": "PROD",
  "ConfigIniPath": "\\\\Srv-paiement64\\customerfiles\\TEST\\[structureId]\\CONFIGSERVER\\config.ini.xml",
  "Cache": {
    //Cache pour la liste des sièges en secondes
    "SeatsAbsoluteExpiration": 120,
    "SeatsSlidingExpiration": 20,
    "SeatsTextAbsoluteExpiration": 600,
    "SeatsTextSlidingExpiration": 120
  },
  "MEETCH": {
    "NbDaysInsuranceValid": 5,
    "InsuranceSubscriptionURL": "https://api.preprod.ticketmate.io/v1/subscriptions",
    "InsuranceSubscriptionApiKey": "API-Key 962255c5ba186c0af27bcf69f38ecddac0da634d6a1c941d83a1709328a464f406081d0cb25b04ea9501ca509166f580b2581082e51f6e3c79c504f017cac63e"
  },
  "CancelOrderCKeyIsNotMandatory": "1",
  "PassCultureHmacKey": "cq_dzQFXbJB2ITwayMfkNDEwuHlDPn8wR1LcBNARTM_cM7-tttw4Rx8z-2ag4vFqprWc0lKqQu0DrmM3ODyRvw"
}
