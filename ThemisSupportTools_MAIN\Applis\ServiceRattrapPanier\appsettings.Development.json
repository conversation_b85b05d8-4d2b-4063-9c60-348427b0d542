{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "FilePaths": {"InclusionFilePath": "D:\\WORK\\Features\\CreateCmd_main\\Applis\\ServiceRattrapPanier\\inclusionsStructures.xml", "ExclusionFilePath": "D:\\WORK\\Features\\CreateCmd_main\\Applis\\ServiceRattrapPanier\\exclusionsStructures.xml"}, "ApplicationSettings": {"isDebugMode": "1", "delaySinceSecondes": 600000, "delayToSecondes": 120}, "typeRun": "TEST", "PathForSqlScript": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\LIBRARIES_SQLSCRIPTS\\{0}\\{1}.sql", "ConfigIniPath": "\\\\*************\\customerfiles\\TEST\\{structureId}\\CONFIGSERVER\\config.ini.xml", "wcf_webPaiement": "http://**************/wstest/wcfPayement/wcf_webPaiement.svc"}