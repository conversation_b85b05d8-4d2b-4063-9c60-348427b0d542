﻿
using Core.Themis.API.Catalog.Helpers.Interfaces;
using Core.Themis.Libraries.BLL;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.DTO.EventsSessions;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace Core.Themis.API.Catalog.Controllers
{
    public class ReservesController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IApiUtilities _apiUtilities;
        private readonly IReservesManager _reservesManager;

        private static readonly RodrigueNLogger Logger = new();
        /// <summary>
        /// Constructeur
        /// </summary>
        /// <param name="configuration"></param>
        /// <param name="memoryCache"></param>
        /// <param name="apiUtilities"></param>
        public ReservesController(
            IConfiguration configuration, 
            IMemoryCache memoryCache, 
            IApiUtilities apiUtilities,
            IReservesManager reservesManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _apiUtilities = apiUtilities;
            _reservesManager = reservesManager;
        }

        /// <summary>
        /// list of all the reserves
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="codeLang"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<ReserveDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 600)]
        [HttpGet]

        [Route("api/{structureId}/ReservesList/{codeLang}")]

        public IActionResult ReservesList(int structureId, string codeLang)
        {
            try
            {
                Logger.Debug(structureId, $"ReservesList({structureId},{codeLang}");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                List<ReserveDTO> listS = _reservesManager.GetAllReserves(structureId, codeLang);

                Logger.Debug(structureId, $"ReservesList({structureId},{codeLang}) ok {listS.Count} reserves to return");

                return Ok(listS);

            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"ReservesList({structureId},{codeLang}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }





    }
}
