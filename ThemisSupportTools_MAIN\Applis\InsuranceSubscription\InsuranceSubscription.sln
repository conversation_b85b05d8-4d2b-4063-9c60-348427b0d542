﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.33627.172
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "InsuranceSubscription", "InsuranceSubscription\InsuranceSubscription.csproj", "{7B6603D6-C4F5-4D09-9A92-1B45DC0B3A8F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Data", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Core.Themis.Libraries.Data.csproj", "{7C7F8D08-A6FE-4FB6-9B48-48B9616B4FE7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLL", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL.csproj", "{02A6E28A-57AF-49B6-B385-E83B26DC5A62}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLLTests", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLLTests\Core.Themis.Libraries.BLLTests.csproj", "{6D9F5595-8194-4D36-9A34-1A42969C939D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DataTests", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Libraries.DataTests\Core.Themis.Libraries.DataTests.csproj", "{7EF53F0F-11F1-48FC-AA46-D4E347D38166}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DTO", "..\..\Libraries\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj", "{8776876A-E8B4-4967-9DCB-4F63814A4E35}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Utilities", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj", "{D4DB2461-AD9A-4BF0-B7DD-2DD0A69FA078}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.UtilitiesTests", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.UtilitiesTests\Core.Themis.Libraries.UtilitiesTests.csproj", "{25C9C188-0276-44D8-B1A2-3B2E1A654BBB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7B6603D6-C4F5-4D09-9A92-1B45DC0B3A8F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7B6603D6-C4F5-4D09-9A92-1B45DC0B3A8F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7B6603D6-C4F5-4D09-9A92-1B45DC0B3A8F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7B6603D6-C4F5-4D09-9A92-1B45DC0B3A8F}.Release|Any CPU.Build.0 = Release|Any CPU
		{7C7F8D08-A6FE-4FB6-9B48-48B9616B4FE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7C7F8D08-A6FE-4FB6-9B48-48B9616B4FE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7C7F8D08-A6FE-4FB6-9B48-48B9616B4FE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7C7F8D08-A6FE-4FB6-9B48-48B9616B4FE7}.Release|Any CPU.Build.0 = Release|Any CPU
		{02A6E28A-57AF-49B6-B385-E83B26DC5A62}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{02A6E28A-57AF-49B6-B385-E83B26DC5A62}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{02A6E28A-57AF-49B6-B385-E83B26DC5A62}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{02A6E28A-57AF-49B6-B385-E83B26DC5A62}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D9F5595-8194-4D36-9A34-1A42969C939D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D9F5595-8194-4D36-9A34-1A42969C939D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D9F5595-8194-4D36-9A34-1A42969C939D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D9F5595-8194-4D36-9A34-1A42969C939D}.Release|Any CPU.Build.0 = Release|Any CPU
		{7EF53F0F-11F1-48FC-AA46-D4E347D38166}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7EF53F0F-11F1-48FC-AA46-D4E347D38166}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7EF53F0F-11F1-48FC-AA46-D4E347D38166}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7EF53F0F-11F1-48FC-AA46-D4E347D38166}.Release|Any CPU.Build.0 = Release|Any CPU
		{8776876A-E8B4-4967-9DCB-4F63814A4E35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8776876A-E8B4-4967-9DCB-4F63814A4E35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8776876A-E8B4-4967-9DCB-4F63814A4E35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8776876A-E8B4-4967-9DCB-4F63814A4E35}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4DB2461-AD9A-4BF0-B7DD-2DD0A69FA078}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4DB2461-AD9A-4BF0-B7DD-2DD0A69FA078}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4DB2461-AD9A-4BF0-B7DD-2DD0A69FA078}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4DB2461-AD9A-4BF0-B7DD-2DD0A69FA078}.Release|Any CPU.Build.0 = Release|Any CPU
		{25C9C188-0276-44D8-B1A2-3B2E1A654BBB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{25C9C188-0276-44D8-B1A2-3B2E1A654BBB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{25C9C188-0276-44D8-B1A2-3B2E1A654BBB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{25C9C188-0276-44D8-B1A2-3B2E1A654BBB}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {504D5348-23DE-479D-BF1B-9C086E1D7E2B}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 9
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://v-dev-test:8080/tfs/appscollection
		SccLocalPath0 = .
		SccProjectUniqueName1 = InsuranceSubscription\\InsuranceSubscription.csproj
		SccProjectName1 = InsuranceSubscription
		SccLocalPath1 = InsuranceSubscription
		SccProjectUniqueName2 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj
		SccProjectName2 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Librairies.Data
		SccLocalPath2 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data
		SccProjectUniqueName3 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj
		SccProjectName3 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLL
		SccLocalPath3 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL
		SccProjectUniqueName4 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests\\Core.Themis.Libraries.BLLTests.csproj
		SccProjectName4 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLLTests
		SccLocalPath4 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests
		SccProjectUniqueName5 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests\\Core.Themis.Libraries.DataTests.csproj
		SccProjectName5 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Libraries.DataTests
		SccLocalPath5 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests
		SccProjectUniqueName6 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj
		SccProjectName6 = ../../Libraries/Core.Themis.Libraries.DTO
		SccLocalPath6 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO
		SccProjectUniqueName7 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj
		SccProjectName7 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.Utilities
		SccLocalPath7 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities
		SccProjectUniqueName8 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests\\Core.Themis.Libraries.UtilitiesTests.csproj
		SccProjectName8 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.UtilitiesTests
		SccLocalPath8 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests
	EndGlobalSection
EndGlobal
