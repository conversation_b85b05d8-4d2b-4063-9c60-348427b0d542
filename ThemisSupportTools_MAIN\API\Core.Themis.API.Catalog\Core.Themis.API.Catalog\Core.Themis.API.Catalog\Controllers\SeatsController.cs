﻿using Core.Themis.API.Catalog.Helpers.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.DTO.Seats;
using Core.Themis.Libraries.Utilities.Crypto;
using Core.Themis.Libraries.Utilities.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Net.Http.Headers;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Core.Themis.API.Catalog.Controllers
{
    [ApiController]
    // [Authorize(Roles = "User,Viewer")]
    public class SeatsController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IMemoryCache _memoryCache;
        private readonly IApiUtilities _apiUtilities;
        private readonly ISessionManager _sessionManager;

        private static readonly RodrigueNLogger Logger = new();

        public SeatsController(
            IConfiguration configuration,
            IMemoryCache memoryCache,
            IApiUtilities apiUtilities,
            ISessionManager sessionManager)
        {
            _configuration = configuration;
            _memoryCache = memoryCache;
            _apiUtilities = apiUtilities;
            _sessionManager = sessionManager;
        }

        /// <summary>
        /// liste des sieges sur une seule zone, etage, section
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="codeLang"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="zoneId"></param>
        /// <param name="floorId"></param>
        /// <param name="sectionId"></param>
        /// <param name="webUserId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<SeatDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpGet]
        [Route("api/{structureId}/SeatsList/{codeLang}/{eventId}/{sessionId}/{zoneId}/{floorId}/{sectionId}/{webUserId}")]
        public IActionResult SeatsList(int structureId, string codeLang, int eventId, int sessionId, int zoneId, int floorId, int sectionId, int webUserId)
        {
            try
            {
                Logger.Debug(structureId, $"SeatList({structureId},{codeLang}, {eventId}, {sessionId}, {zoneId}, {floorId}, {sectionId}, {webUserId})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                System.Diagnostics.Debug.WriteLine($"SeatsList {codeLang}/{eventId}/{sessionId}/{zoneId}/{floorId}/{sectionId}/{webUserId}... {DateTime.Now.ToString("hh:mm:ss:fff")}...");

                List<SeatDTO> seatsList = _sessionManager.LoadSeatPlan(structureId, 0, eventId, sessionId, zoneId, floorId, sectionId, 0, "", codeLang, webUserId);

                Logger.Debug(structureId, $"SeatList({structureId},{codeLang}, {eventId}, {sessionId}, {zoneId}, {floorId}, {sectionId}) ok {seatsList.Count} seats to return");

                return Ok(seatsList);
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"SeatList({structureId},{codeLang}, {eventId}, {sessionId}, {zoneId}, {floorId}, {sectionId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// liste des sieges indispo (etat X ou I) sur plusieures zones, etages, sections
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="codeLang"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="listZonesId"></param>
        /// <param name="listFloorsId"></param>
        /// <param name="listSectionsId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<SeatDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpGet]

        [Route("api/{structureId}/SeatsListIndispo/{codeLang}/{eventId}/{sessionId}")]
        public IActionResult SeatsListIndispo(int structureId, string codeLang, int eventId, int sessionId,
            [FromQueryAttribute] List<int> listZonesId, [FromQueryAttribute] List<int> listFloorsId, [FromQueryAttribute] List<int> listSectionsId)
        {

            System.Diagnostics.Debug.WriteLine($"SeatsListIndispo {codeLang}/{eventId}/{sessionId}... {DateTime.Now.ToString("hh:mm:ss:fff")}...");

            string sListZonesId = string.Join(";", listZonesId);
            string sListFloorsId = string.Join(";", listFloorsId);
            string sListSesctionId = string.Join(";", listSectionsId);

            try
            {

                if (listZonesId.Count == 0) listZonesId.Add(0);
                if (listFloorsId.Count == 0) listFloorsId.Add(0);
                if (listSectionsId.Count == 0) listSectionsId.Add(0);

                Logger.Debug(structureId, $"SeatList({structureId},{codeLang}, {eventId}, {sessionId}, {sListZonesId}, {sListFloorsId}, {sListSesctionId})...");

                string typeRun = _configuration["TypeRun"].ToString();
                var pathScriptSqlCommons = _configuration["PathScriptSqlCommons"].ToString();
                var wsAdminConnectionString = _configuration.GetConnectionString("WsAdminDB");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                string seatsListCache = $"seatsListCache_{structureId}_{eventId}_{sessionId}_{sListZonesId}_{sListFloorsId}_{sListSesctionId}_{codeLang}";
                List<SeatDTO> seatsList = null;
                try
                {
                    _memoryCache.TryGetValue(seatsListCache, out seatsList);

                    if (seatsList == null)
                    {
                        seatsList = _sessionManager.LoadSeatPlanIndispo(structureId, 0, eventId, sessionId, listZonesId, listFloorsId, listSectionsId, codeLang);

                        //Mets en cache 
                        var cacheExpiryOptions = new MemoryCacheEntryOptions
                        {
                            AbsoluteExpiration = DateTime.Now.AddSeconds(int.Parse(_configuration["Cache:SeatsAbsoluteExpiration"].ToString())),
                            Priority = CacheItemPriority.High,
                            SlidingExpiration = TimeSpan.FromSeconds(int.Parse(_configuration["Cache:SeatsSlidingExpiration"].ToString()))
                        };
                        _memoryCache.Set(seatsListCache, seatsList, cacheExpiryOptions);

                    }
                }
                catch (Exception ex)
                {
                    throw;
                }

                // List<SeatEntity> seats = SessionManager.LoadSeatPlan(sqlConnOpen, structureId, 0, eventId, sessionId, zoneId, floorId, sectionId, 0, "", codeLang, pathScriptSqlCommons);

                Logger.Debug(structureId, $"SeatList({structureId},{codeLang}, {eventId}, {sessionId}, {sListZonesId}, {sListFloorsId}, {sListSesctionId}) ok {seatsList.Count} seats to return");

                return Ok(seatsList);
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"SeatList({structureId},{codeLang}, {eventId}, {sessionId}, {sListZonesId}, {sListFloorsId}, {sListSesctionId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }



        /// <summary>
        /// liste des sieges sur plusieures zones, etages, sections
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="codeLang"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="listZonesId"></param>
        /// <param name="listFloorsId"></param>
        /// <param name="listSectionsId"></param>
        /// <param name="webUserId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<SeatDTO>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 5)]
        [HttpGet]

        [Route("api/{structureId}/SeatsList/{codeLang}/{eventId}/{sessionId}/{webUserId}")]
        public IActionResult SeatsList(int structureId, string codeLang, int eventId, int sessionId,
            [FromQueryAttribute] List<int> listZonesId, [FromQueryAttribute] List<int> listFloorsId, [FromQueryAttribute] List<int> listSectionsId,
            int webUserId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"SeatsList {codeLang}/{eventId}/{sessionId}/{webUserId}... {DateTime.Now.ToString("hh:mm:ss:fff")}...");


                Logger.Debug(structureId, $"SeatList({structureId},{codeLang}, {eventId}, {sessionId}, " +
                    $"{string.Join(";", listZonesId)}, {string.Join(";", listFloorsId)}, {string.Join(";", listSectionsId)}, " +
                    $"{webUserId})...");

                string typeRun = _configuration["TypeRun"].ToString();
                var pathScriptSqlCommons = _configuration["PathScriptSqlCommons"].ToString();
                var wsAdminConnectionString = _configuration.GetConnectionString("WsAdminDB");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                string seatsListCache = $"seatsListCache_{structureId}_{eventId}_{sessionId}_{string.Join(".", listZonesId)}_{string.Join(".", listFloorsId)}_{string.Join(".", listSectionsId)}_{codeLang}";
                List<SeatDTO> seatsList = null;
                try
                {
                    _memoryCache.TryGetValue(seatsListCache, out seatsList);
                    if (seatsList == null)
                    {
                        seatsList = _sessionManager.LoadSeatPlan(structureId, 0, eventId, sessionId, listZonesId, listFloorsId, listSectionsId, 0, "", codeLang, webUserId);

                        //Mets en cache 
                        var cacheExpiryOptions = new MemoryCacheEntryOptions
                        {
                            AbsoluteExpiration = DateTime.Now.AddSeconds(int.Parse(_configuration["Cache:SeatsAbsoluteExpiration"].ToString())),
                            Priority = CacheItemPriority.High,
                            SlidingExpiration = TimeSpan.FromSeconds(int.Parse(_configuration["Cache:SeatsSlidingExpiration"].ToString()))
                        };
                        _memoryCache.Set(seatsListCache, seatsList, cacheExpiryOptions);
                    }
                }
                catch (Exception ex)
                {
                    throw;
                }

                Logger.Debug(structureId, $"SeatList({structureId},{codeLang}, {eventId}, {sessionId}, {string.Join(";", listZonesId)}, {string.Join(";", listFloorsId)}, {string.Join(";", listSectionsId)}) ok {seatsList.Count} seats to return");

                return Ok(seatsList);
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"SeatList({structureId},{codeLang}, {eventId}, {sessionId}, {string.Join(";", listZonesId)}, {string.Join(";", listFloorsId)}, {string.Join(";", listSectionsId)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// les textes, textes longs, lignes et poteaux
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="listFloorsId"></param>
        /// <param name="floorId"></param>
        /// <param name="sectionId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ListSeatingPlanObjectsViewModel))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 60)]
        [HttpGet]

        [Route("api/{structureId}/TextsList/{eventId}/{sessionId}/{zoneId}/{floorId}/{sectionId}")]
        public IActionResult TextsList(int structureId, int eventId, int sessionId, int zoneId, int floorId, int sectionId)
        {
            try
            {

                System.Diagnostics.Debug.WriteLine($"TextsList {structureId}, {eventId}, {sessionId}, {zoneId}, {floorId}, {sectionId}... {DateTime.Now.ToString("hh:mm:ss:fff")}...");


                Logger.Debug(structureId, $"TextsList({structureId}, {eventId}, {sessionId}, {zoneId}, {floorId}, {sectionId})...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);


                var seatsList = _sessionManager.LoadTextsPlan(structureId, 0, eventId, sessionId, zoneId, floorId, sectionId);

                List<TextShortSeatingPlan> listShortText = seatsList.Where(s => s.Type_Siege == "isTexte").Select(s => (TextShortSeatingPlan)s).ToList();
                List<TextLongSeatingPlanEntity> listLongText = seatsList.Where(s => s.Type_Siege == "TexteLong").Select(s => (TextLongSeatingPlanEntity)s).ToList();
                List<LineSeatingPlanEntity> listLines = seatsList.Where(s => s.Type_Siege == "isTrait1" || s.Type_Siege == "isTrait2").Select(s => (LineSeatingPlanEntity)s).ToList();
                List<PoleSeatingPlanEntity> listPoteaux = seatsList.Where(s => s.Type_Siege == "isPoteau").Select(s => (PoleSeatingPlanEntity)s).ToList();

                ListSeatingPlanObjectsViewModel ob = new ListSeatingPlanObjectsViewModel()
                {
                    listLongTexts = listLongText,
                    listShortTexts = listShortText,
                    listPoles = listPoteaux,
                    listLines = listLines
                };

                Logger.Debug(structureId, $"TextsList({structureId}, {eventId}, {sessionId}, {zoneId}, {floorId}, {sectionId}) ok");

                return Ok(ob);
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"TextsList({structureId}, {eventId}, {sessionId}, {zoneId}, {floorId}, {sectionId}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// les textes, textes longs, lignes et poteaux sur plusieurs zones, etages, sections
        /// </summary>
        /// <param name="structureId"></param>
        /// <param name="eventId"></param>
        /// <param name="sessionId"></param>
        /// <param name="listZonesId"></param>
        /// <param name="listFloorsId"></param>
        /// <param name="listSectionsId"></param>
        /// <returns></returns>
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ListSeatingPlanObjectsViewModel))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ResponseCache(VaryByHeader = "User-Agent", Duration = 60)]
        [HttpGet]

        [Route("api/{structureId}/TextsList/{eventId}/{sessionId}")]
        public IActionResult TextsList(int structureId, int eventId, int sessionId,
            [FromQueryAttribute] List<int> listZonesId, [FromQueryAttribute] List<int> listFloorsId, [FromQueryAttribute] List<int> listSectionsId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"TextsList 2 {structureId}, {eventId}, {sessionId}, {listFloorsId}... {DateTime.Now.ToString("hh:mm:ss:fff")}...");


                Logger.Debug(structureId, $"TextsList({structureId}, {eventId}, {sessionId}, " +
                $"{string.Join(";", listZonesId)}, {string.Join(";", listFloorsId)}, {string.Join(";", listSectionsId)}, " +
                $")...");

                var accT = Request.Headers[HeaderNames.Authorization];
                if (!TokenManager.PartnerHasRight(structureId, accT))
                    return Problem($"structureId {structureId} is not allowed", null, StatusCodes.Status401Unauthorized);

                string seatsListCache = $"TextsList{structureId}_{eventId}_{sessionId}_{string.Join(".", listZonesId)}_{string.Join(".", listFloorsId)}_{string.Join(".", listSectionsId)}";

                ListSeatingPlanObjectsViewModel ob = null;

                try
                {
                  
                    if (ob == null)
                    {
                        var seatsList = _sessionManager.LoadTextsPlan(structureId, 0, eventId, sessionId, listZonesId, listFloorsId, listSectionsId);

                        List<TextShortSeatingPlan> listShortText = seatsList.Where(s => s.Type_Siege == "isTexte").Select(s => (TextShortSeatingPlan)s).ToList();
                        List<TextLongSeatingPlanEntity> listLongText = seatsList.Where(s => s.Type_Siege == "TexteLong").Select(s => (TextLongSeatingPlanEntity)s).ToList();
                        List<LineSeatingPlanEntity> listLines = seatsList.Where(s => s.Type_Siege == "isTrait1" || s.Type_Siege == "isTrait2").Select(s => (LineSeatingPlanEntity)s).ToList();
                        List<PoleSeatingPlanEntity> listPoteaux = seatsList.Where(s => s.Type_Siege == "isPoteau").Select(s => (PoleSeatingPlanEntity)s).ToList();

                        ob = new ListSeatingPlanObjectsViewModel()
                        {
                            listLongTexts = listLongText,
                            listShortTexts = listShortText,
                            listPoles = listPoteaux,
                            listLines = listLines
                        };
                    }
                }
                catch (Exception ex)
                {
                    throw;
                }

                Logger.Debug(structureId, $"TextsList({structureId}, {eventId}, {sessionId}, {listZonesId}, {listFloorsId}, {listSectionsId}) ok");

                return Ok(ob);
            }
            catch (Exception ex)
            {
                // throw ex;
                Logger.Error(structureId, ex, $"TextsList({structureId}, {eventId}, {sessionId}, {string.Join(";", listZonesId)}, {string.Join(";", listFloorsId)}, {string.Join(";", listSectionsId)}) error");
                return Problem(ex.Message, null, StatusCodes.Status500InternalServerError);
            }
        }
    }
}
