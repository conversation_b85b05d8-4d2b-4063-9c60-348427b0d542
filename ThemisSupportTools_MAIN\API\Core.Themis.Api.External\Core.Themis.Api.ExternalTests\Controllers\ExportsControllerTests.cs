﻿using Core.Themis.Api.External.Controllers;
//using Core.Themis.Libraries.BLL.Export.Interfaces;
using Core.Themis.Libraries.BLL.Extentions.ServicesBuilder;
using Core.Themis.Libraries.BLL.Extentions.UnitTest;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Export.Interfaces;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.BLL.WsAdmin.Interfaces;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Partner;
using Core.Themis.Libraries.Utilities.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Themis.Api.ExternalTests.Controllers
{
    [TestClass()]
    public class ExportsControllerTests
    {

        public ExportsControllerTests()
        {
            var services = new ServiceCollection();

            services.AddRodrigueConfigurationForUnitTest();
            services.AddRodrigueDataServices();
            services.AddRodrigueManager();
            services.AddRodrigueMapper();
            services.AddMemoryCache();

            var serviceProvider = services.BuildServiceProvider();

            //_priceManager = serviceProvider.GetRequiredService<IPriceManager>();
            //_seatManager = serviceProvider.GetRequiredService<ISeatManager>();
            //_translateManager = serviceProvider.GetRequiredService<ITranslateManager>();
            ConfigurationHelper.Initialize(serviceProvider.GetRequiredService<IConfiguration>());
        }


        [TestMethod]
        [Ignore("ne passe pas en vrai, trouver comment passer le header autorization")]
        public void GetExportByScriptName_returnNotNull()
        {
            IExportManager _exportManager = null;
            ILogsPartnerManager _logsPartnerManager = null;
            LogsPartenairesEntity _logsPartenairesEntity = null;
            //var exportManager = new IExportManager();

            Microsoft.Extensions.Primitives.StringValues accT = new Microsoft.Extensions.Primitives.StringValues();


            var control = new ExportController(_exportManager, _logsPartnerManager);
            var res = control.GetExportByScriptName(null);
            //ExportController.GetExportByScriptName()

             
            Assert.IsNotNull(res);


        }
    }
}
