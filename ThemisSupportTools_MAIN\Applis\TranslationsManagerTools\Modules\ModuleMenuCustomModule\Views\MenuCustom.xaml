﻿<UserControl x:Class="ModuleMenuCustom.Views.MenuCustom"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ModuleMenuCustom.Views"
             xmlns:prism="http://prismlibrary.com/"
             prism:ViewModelLocator.AutoWireViewModel="True"
              xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    
    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Height="Auto">

        <Button Content="Sections" Command="{Binding ManageCommand}"  CommandParameter="SectionsManage" Margin="8" Padding="8"  Visibility="{Binding TranslatorMode}"  />
        <Button Content="Variables" Command="{Binding ManageCommand}" CommandParameter="VariablesManage" Margin="8,0" Padding="8"  Visibility="{Binding TranslatorMode}"/>
        <Button Content="Traductions" Command="{Binding ManageCommand}" CommandParameter="TranslationsManage" Margin="8 8 8 0" Padding="8" Cursor="Hand"/>
        <Button Content="Plateformes" Command="{Binding ManageCommand}" CommandParameter="PlatformsTranslations" Margin="8" Padding="8" Cursor="Hand"/>



        <Button  Command="{Binding ManageCommand}" CommandParameter="ApplicationSettings" Margin="8 0" Style="{DynamicResource MaterialDesignOutlinedDarkButton}"  HorizontalAlignment="Right" VerticalAlignment="Center">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="SettingsApplications"/>
                <TextBlock Text="Paramètres"/>
            </StackPanel>
        </Button>

    </StackPanel>
</UserControl>
