﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<location path="." inheritInChildApplications="false">
		<system.webServer>
			<handlers>
				<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
			</handlers>
			<aspNetCore processPath="dotnet" arguments=".\Core.Themis.API.Customers.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" >
				<environmentVariables>
					<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Staging" />
				</environmentVariables>
			</aspNetCore>
		</system.webServer>
	</location>
</configuration>
<!--ProjectGuid: dc45fe3b-2e19-4b1d-b116-2aaa34a2d16b-->