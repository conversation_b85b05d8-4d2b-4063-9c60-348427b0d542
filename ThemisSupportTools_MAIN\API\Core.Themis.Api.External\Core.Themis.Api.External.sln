﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32922.545
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Api.External", "Core.Themis.Api.External\Core.Themis.Api.External.csproj", "{92966074-6EB1-4B36-BF77-96586D7F585D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Api.ExternalTests", "Core.Themis.Api.ExternalTests\Core.Themis.Api.ExternalTests.csproj", "{139C2432-CB6D-4C8A-B19F-3B49812B22CE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLL", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLL.csproj", "{E4CDD9AD-9EF6-48C2-9E2F-1BCBCA794696}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.BLLTests", "..\..\Libraries\Core.Themis.Libraries.BLL\Core.Themis.Libraries.BLLTests\Core.Themis.Libraries.BLLTests.csproj", "{C4D1A5D3-C8FB-4B93-A125-8C66FD3ED5C1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Data", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Librairies.Data\Core.Themis.Libraries.Data.csproj", "{407D8573-AF08-4143-817F-E1F33377F364}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DataTests", "..\..\Libraries\Core.Themis.Libraries.Data\Core.Themis.Libraries.DataTests\Core.Themis.Libraries.DataTests.csproj", "{8F2C0973-34BC-4249-8B95-2D4FB77AE541}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.DTO", "..\..\Libraries\Core.Themis.Libraries.DTO\Core.Themis.Libraries.DTO.csproj", "{5E84566F-E65A-43EB-9262-D74B49D41F51}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.Utilities", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.Utilities.csproj", "{AA1025B9-98CD-4607-BFB2-462E63B16F8B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core.Themis.Libraries.UtilitiesTests", "..\..\Libraries\Core.Themis.Libraries.Utilities\Core.Themis.Libraries.UtilitiesTests\Core.Themis.Libraries.UtilitiesTests.csproj", "{1B5D3637-FD92-4055-87A8-7F40DA73A050}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{92966074-6EB1-4B36-BF77-96586D7F585D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92966074-6EB1-4B36-BF77-96586D7F585D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92966074-6EB1-4B36-BF77-96586D7F585D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{92966074-6EB1-4B36-BF77-96586D7F585D}.Release|Any CPU.Build.0 = Release|Any CPU
		{139C2432-CB6D-4C8A-B19F-3B49812B22CE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{139C2432-CB6D-4C8A-B19F-3B49812B22CE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{139C2432-CB6D-4C8A-B19F-3B49812B22CE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{139C2432-CB6D-4C8A-B19F-3B49812B22CE}.Release|Any CPU.Build.0 = Release|Any CPU
		{E4CDD9AD-9EF6-48C2-9E2F-1BCBCA794696}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E4CDD9AD-9EF6-48C2-9E2F-1BCBCA794696}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E4CDD9AD-9EF6-48C2-9E2F-1BCBCA794696}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E4CDD9AD-9EF6-48C2-9E2F-1BCBCA794696}.Release|Any CPU.Build.0 = Release|Any CPU
		{C4D1A5D3-C8FB-4B93-A125-8C66FD3ED5C1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C4D1A5D3-C8FB-4B93-A125-8C66FD3ED5C1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C4D1A5D3-C8FB-4B93-A125-8C66FD3ED5C1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C4D1A5D3-C8FB-4B93-A125-8C66FD3ED5C1}.Release|Any CPU.Build.0 = Release|Any CPU
		{407D8573-AF08-4143-817F-E1F33377F364}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{407D8573-AF08-4143-817F-E1F33377F364}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{407D8573-AF08-4143-817F-E1F33377F364}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{407D8573-AF08-4143-817F-E1F33377F364}.Release|Any CPU.Build.0 = Release|Any CPU
		{8F2C0973-34BC-4249-8B95-2D4FB77AE541}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8F2C0973-34BC-4249-8B95-2D4FB77AE541}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8F2C0973-34BC-4249-8B95-2D4FB77AE541}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8F2C0973-34BC-4249-8B95-2D4FB77AE541}.Release|Any CPU.Build.0 = Release|Any CPU
		{5E84566F-E65A-43EB-9262-D74B49D41F51}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5E84566F-E65A-43EB-9262-D74B49D41F51}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5E84566F-E65A-43EB-9262-D74B49D41F51}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5E84566F-E65A-43EB-9262-D74B49D41F51}.Release|Any CPU.Build.0 = Release|Any CPU
		{AA1025B9-98CD-4607-BFB2-462E63B16F8B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AA1025B9-98CD-4607-BFB2-462E63B16F8B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AA1025B9-98CD-4607-BFB2-462E63B16F8B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AA1025B9-98CD-4607-BFB2-462E63B16F8B}.Release|Any CPU.Build.0 = Release|Any CPU
		{1B5D3637-FD92-4055-87A8-7F40DA73A050}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1B5D3637-FD92-4055-87A8-7F40DA73A050}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1B5D3637-FD92-4055-87A8-7F40DA73A050}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1B5D3637-FD92-4055-87A8-7F40DA73A050}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {1AC2A055-6B08-4AEA-9C1B-0D5BCBE5FDF2}
	EndGlobalSection
	GlobalSection(TeamFoundationVersionControl) = preSolution
		SccNumberOfProjects = 10
		SccEnterpriseProvider = {4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}
		SccTeamFoundationServer = http://v-dev-test:8080/tfs/appscollection
		SccLocalPath0 = .
		SccProjectUniqueName1 = Core.Themis.Api.External\\Core.Themis.Api.External.csproj
		SccProjectName1 = Core.Themis.Api.External
		SccLocalPath1 = Core.Themis.Api.External
		SccProjectUniqueName2 = Core.Themis.Api.ExternalTests\\Core.Themis.Api.ExternalTests.csproj
		SccProjectName2 = Core.Themis.Api.ExternalTests
		SccLocalPath2 = Core.Themis.Api.ExternalTests
		SccProjectUniqueName3 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj
		SccProjectName3 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLL
		SccLocalPath3 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL
		SccProjectUniqueName4 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests\\Core.Themis.Libraries.BLLTests.csproj
		SccProjectName4 = ../../Libraries/Core.Themis.Libraries.BLL/Core.Themis.Libraries.BLLTests
		SccLocalPath4 = ..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLLTests
		SccProjectUniqueName5 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj
		SccProjectName5 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Librairies.Data
		SccLocalPath5 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data
		SccProjectUniqueName6 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests\\Core.Themis.Libraries.DataTests.csproj
		SccProjectName6 = ../../Libraries/Core.Themis.Libraries.Data/Core.Themis.Libraries.DataTests
		SccLocalPath6 = ..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Libraries.DataTests
		SccProjectUniqueName7 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj
		SccProjectName7 = ../../Libraries/Core.Themis.Libraries.DTO
		SccLocalPath7 = ..\\..\\Libraries\\Core.Themis.Libraries.DTO
		SccProjectUniqueName8 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities.csproj
		SccProjectName8 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.Utilities
		SccLocalPath8 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.Utilities
		SccProjectUniqueName9 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests\\Core.Themis.Libraries.UtilitiesTests.csproj
		SccProjectName9 = ../../Libraries/Core.Themis.Libraries.Utilities/Core.Themis.Libraries.UtilitiesTests
		SccLocalPath9 = ..\\..\\Libraries\\Core.Themis.Libraries.Utilities\\Core.Themis.Libraries.UtilitiesTests
	EndGlobalSection
EndGlobal
